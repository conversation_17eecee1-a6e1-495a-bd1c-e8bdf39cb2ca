import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { locales, getDirection, type Locale } from '../../lib/i18n';
import '../globals.css';

export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export const metadata: Metadata = {
  title: 'VidMeet - معدات الضيافة',
  description: 'موقع متخصص في معدات المطاعم والفنادق',
};

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale: localeParam } = await params;

  // التحقق من صحة اللغة
  if (!locales.includes(localeParam as Locale)) {
    notFound();
  }

  const locale = localeParam as Locale;
  const direction = getDirection(locale);

  return (
    <div lang={locale} dir={direction} className={`${direction === 'rtl' ? 'rtl' : 'ltr'} font-tajawal min-h-screen`}>
      {children}
    </div>
  );
}
