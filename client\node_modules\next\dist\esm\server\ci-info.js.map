{"version": 3, "sources": ["../../src/server/ci-info.ts"], "sourcesContent": ["import ciEnvironment from 'next/dist/compiled/ci-info'\n\nconst { isCI: _isCI, name: _name } = ciEnvironment\n\nconst isZeitNow = !!process.env.NOW_BUILDER\n\nconst envStack = process.env.STACK\nconst isHeroku =\n  typeof envStack === 'string' && envStack.toLowerCase().includes('heroku')\n\nexport const isCI = isZeitNow || isHeroku || _isCI\nexport const name = isZeitNow ? 'ZEIT Now' : isHeroku ? 'Heroku' : _name\n\n// This boolean indicates if the CI platform has first-class Next.js support,\n// which allows us to disable certain messages which do not require their\n// action.\nexport const hasNextSupport = Boolean(isZeitNow)\n"], "names": ["ciEnvironment", "isCI", "_isCI", "name", "_name", "isZeitNow", "process", "env", "NOW_BUILDER", "envStack", "STACK", "isHeroku", "toLowerCase", "includes", "hasNextSupport", "Boolean"], "mappings": "AAAA,OAAOA,mBAAmB,6BAA4B;AAEtD,MAAM,EAAEC,MAAMC,KAAK,EAAEC,MAAMC,KAAK,EAAE,GAAGJ;AAErC,MAAMK,YAAY,CAAC,CAACC,QAAQC,GAAG,CAACC,WAAW;AAE3C,MAAMC,WAAWH,QAAQC,GAAG,CAACG,KAAK;AAClC,MAAMC,WACJ,OAAOF,aAAa,YAAYA,SAASG,WAAW,GAAGC,QAAQ,CAAC;AAElE,OAAO,MAAMZ,OAAOI,aAAaM,YAAYT,MAAK;AAClD,OAAO,MAAMC,OAAOE,YAAY,aAAaM,WAAW,WAAWP,MAAK;AAExE,6EAA6E;AAC7E,yEAAyE;AACzE,UAAU;AACV,OAAO,MAAMU,iBAAiBC,QAAQV,WAAU"}