import React from 'react';
import Link from 'next/link';
import { Locale } from '../lib/i18n';
import { getTranslation } from '../lib/translations';
import { useFooterSettings, useSocialLinks, useContactInfo, useSiteSettings } from '../src/hooks/useSiteSettings';

interface FooterProps {
  locale: Locale;
}

const Footer: React.FC<FooterProps> = ({ locale }) => {
  const t = (key: string) => getTranslation(locale, key as any);
  const { footerSettings } = useFooterSettings();
  const { socialLinks } = useSocialLinks();
  const { contactInfo } = useContactInfo();
  const { settings } = useSiteSettings();

  return (
    <footer className="bg-gradient-to-r from-gray-900 to-gray-800 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <h3 className="text-2xl font-bold text-primary">
              {locale === 'ar' ? (settings?.siteNameAr || 'دروب حاجر') : (settings?.siteName || 'DROOB HAJER')}
            </h3>
            <p className="text-gray-300 leading-relaxed">
              {locale === 'ar'
                ? (footerSettings?.companyInfo?.descriptionAr || 'نحن مورد رائد لمعدات المطاعم والفنادق عالية الجودة، نقدم حلولاً شاملة لصناعة الضيافة.')
                : (footerSettings?.companyInfo?.description || 'We are a leading supplier of high-quality restaurant and hotel equipment, providing comprehensive solutions for the hospitality industry.')
              }
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">
              {locale === 'ar' ? 'روابط سريعة' : 'Quick Links'}
            </h4>
            <ul className="space-y-2">
              {footerSettings?.quickLinks?.filter(link => link.isActive).map((link, index) => (
                <li key={index}>
                  <Link href={link.url.startsWith('/') ? `/${locale}${link.url === '/' ? '' : link.url}` : link.url} className="text-gray-300 hover:text-primary transition-colors duration-200">
                    {locale === 'ar' ? link.nameAr : link.nameEn}
                  </Link>
                </li>
              )) || (
                <>
                  <li>
                    <Link href={`/${locale}`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                      {t('home')}
                    </Link>
                  </li>
                  <li>
                    <Link href={`/${locale}/products`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                      {t('products')}
                    </Link>
                  </li>
                  <li>
                    <Link href={`/${locale}/categories`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                      {t('categories')}
                    </Link>
                  </li>
                  <li>
                    <Link href={`/${locale}/about`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                      {t('about')}
                    </Link>
                  </li>
                  <li>
                    <Link href={`/${locale}/contact`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                      {t('contact')}
                    </Link>
                  </li>
                </>
              )}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold">
              {locale === 'ar' ? 'معلومات التواصل' : 'Contact Info'}
            </h4>
            <ul className="space-y-3">
              {footerSettings?.companyInfo?.showPhone && contactInfo?.phone && (
                <li className="flex items-center space-x-3 space-x-reverse">
                  <i className="ri-phone-line text-primary"></i>
                  <a href={`tel:${contactInfo.phone}`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                    {contactInfo.phone}
                  </a>
                </li>
              )}
              {footerSettings?.companyInfo?.showEmail && contactInfo?.email && (
                <li className="flex items-center space-x-3 space-x-reverse">
                  <i className="ri-mail-line text-primary"></i>
                  <a href={`mailto:${contactInfo.email}`} className="text-gray-300 hover:text-primary transition-colors duration-200">
                    {contactInfo.email}
                  </a>
                </li>
              )}
              {footerSettings?.companyInfo?.showAddress && contactInfo?.address && (
                <li className="flex items-center space-x-3 space-x-reverse">
                  <i className="ri-map-pin-line text-primary"></i>
                  <span className="text-gray-300">
                    {locale === 'ar' ? contactInfo.addressAr : contactInfo.address}
                  </span>
                </li>
              )}
            </ul>
          </div>

          {/* Social Media */}
          {footerSettings?.showSocialLinks && (
            <div className="space-y-4">
              <h4 className="text-lg font-semibold">
                {locale === 'ar' ? 'تابعنا' : 'Follow Us'}
              </h4>
              <div className="flex space-x-4 space-x-reverse">
                {socialLinks?.facebook && (
                  <a href={socialLinks.facebook} target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-gray-700 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-200">
                    <i className="ri-facebook-fill"></i>
                  </a>
                )}
                {socialLinks?.instagram && (
                  <a href={socialLinks.instagram} target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-gray-700 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-200">
                    <i className="ri-instagram-line"></i>
                  </a>
                )}
                {socialLinks?.twitter && (
                  <a href={socialLinks.twitter} target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-gray-700 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-200">
                    <i className="ri-twitter-line"></i>
                  </a>
                )}
                {socialLinks?.linkedin && (
                  <a href={socialLinks.linkedin} target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-gray-700 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-200">
                    <i className="ri-linkedin-fill"></i>
                  </a>
                )}
                {socialLinks?.youtube && (
                  <a href={socialLinks.youtube} target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-gray-700 hover:bg-primary rounded-full flex items-center justify-center transition-colors duration-200">
                    <i className="ri-youtube-fill"></i>
                  </a>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8 text-center">
          <p className="text-gray-400">
            © 2024 {locale === 'ar' ? (settings?.siteNameAr || 'دروب حاجر') : (settings?.siteName || 'Droob Hajer')}. {' '}
            {locale === 'ar'
              ? (footerSettings?.copyrightTextAr || 'جميع الحقوق محفوظة.')
              : (footerSettings?.copyrightText || 'All rights reserved.')
            }
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
