'use client';

import React, { useState, useEffect } from 'react';
import { Locale } from '../lib/i18n';

interface ServicesSectionProps {
  locale: Locale;
}

const ServicesSection: React.FC<ServicesSectionProps> = ({ locale }) => {
  const [whatsappNumber, setWhatsappNumber] = useState('+966501234567');

  useEffect(() => {
    // قراءة رقم الواتساب من الإعدادات
    const savedSettings = localStorage.getItem('siteSettings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        // استخدام فقط إعدادات التواصل الجديدة، تجاهل الرقم القديم
        if (settings.communicationSettings?.whatsapp?.businessNumber) {
          setWhatsappNumber(settings.communicationSettings.whatsapp.businessNumber);
        }
      } catch (error) {
        console.log('Using default WhatsApp number');
      }
    }
  }, []);
  const services = [
    {
      icon: 'ri-tools-line',
      title: locale === 'ar' ? 'التركيب والصيانة' : 'Installation & Maintenance',
      description: locale === 'ar' 
        ? 'نوفر خدمات التركيب المهني والصيانة الدورية لجميع المعدات'
        : 'We provide professional installation and regular maintenance for all equipment',
      features: [
        locale === 'ar' ? 'تركيب احترافي' : 'Professional installation',
        locale === 'ar' ? 'صيانة دورية' : 'Regular maintenance',
        locale === 'ar' ? 'دعم فني 24/7' : '24/7 technical support'
      ]
    },
    {
      icon: 'ri-truck-line',
      title: locale === 'ar' ? 'التوصيل السريع' : 'Fast Delivery',
      description: locale === 'ar' 
        ? 'خدمة توصيل سريعة وآمنة لجميع أنحاء المملكة والخليج'
        : 'Fast and secure delivery service throughout the Kingdom and Gulf',
      features: [
        locale === 'ar' ? 'توصيل مجاني' : 'Free delivery',
        locale === 'ar' ? 'تغليف آمن' : 'Safe packaging',
        locale === 'ar' ? 'تتبع الشحنة' : 'Shipment tracking'
      ]
    },
    {
      icon: 'ri-customer-service-2-line',
      title: locale === 'ar' ? 'الاستشارة المجانية' : 'Free Consultation',
      description: locale === 'ar' 
        ? 'فريق من الخبراء لتقديم الاستشارة المجانية وتصميم الحلول المناسبة'
        : 'Team of experts to provide free consultation and design suitable solutions',
      features: [
        locale === 'ar' ? 'استشارة مجانية' : 'Free consultation',
        locale === 'ar' ? 'تصميم مخصص' : 'Custom design',
        locale === 'ar' ? 'خبرة 15+ سنة' : '15+ years experience'
      ]
    },
    {
      icon: 'ri-shield-check-line',
      title: locale === 'ar' ? 'الضمان الشامل' : 'Comprehensive Warranty',
      description: locale === 'ar' 
        ? 'ضمان شامل على جميع المنتجات مع خدمة ما بعد البيع المتميزة'
        : 'Comprehensive warranty on all products with excellent after-sales service',
      features: [
        locale === 'ar' ? 'ضمان 5 سنوات' : '5-year warranty',
        locale === 'ar' ? 'قطع غيار أصلية' : 'Original spare parts',
        locale === 'ar' ? 'خدمة ما بعد البيع' : 'After-sales service'
      ]
    }
  ];

  const content = {
    ar: {
      title: 'خدماتنا المتميزة',
      subtitle: 'نقدم مجموعة شاملة من الخدمات لضمان رضاكم التام',
      cta: 'اطلب خدمة',
      learnMore: 'اعرف المزيد'
    },
    en: {
      title: 'Our Distinguished Services',
      subtitle: 'We provide a comprehensive range of services to ensure your complete satisfaction',
      cta: 'Request Service',
      learnMore: 'Learn More'
    }
  };

  const currentContent = content[locale];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">
            {currentContent.title}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {currentContent.subtitle}
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className="group bg-gray-50 rounded-2xl p-8 hover:bg-white hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-transparent hover:border-primary/20"
            >
              {/* Icon */}
              <div className="w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center mb-6 group-hover:bg-primary group-hover:scale-110 transition-all duration-300">
                <i className={`${service.icon} text-3xl text-primary group-hover:text-white`}></i>
              </div>

              {/* Content */}
              <h3 className="text-xl font-bold text-gray-800 mb-4 group-hover:text-primary transition-colors duration-300">
                {service.title}
              </h3>
              
              <p className="text-gray-600 mb-6 leading-relaxed">
                {service.description}
              </p>

              {/* Features */}
              <ul className="space-y-2 mb-6">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2"></i>
                    {feature}
                  </li>
                ))}
              </ul>

              {/* CTA Button */}
              <a
                href={`/${locale}/contact`}
                className="inline-flex items-center gap-2 text-primary font-semibold hover:text-primary/80 transition-colors duration-300 group-hover:gap-3"
              >
                {currentContent.learnMore}
                <i className="ri-arrow-right-line transition-transform duration-300 group-hover:translate-x-1"></i>
              </a>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-primary to-primary/90 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              {locale === 'ar' 
                ? 'هل تحتاج إلى خدمة مخصصة؟' 
                : 'Need a custom service?'
              }
            </h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto">
              {locale === 'ar' 
                ? 'تواصل معنا للحصول على حلول مخصصة تناسب احتياجاتك الخاصة'
                : 'Contact us to get custom solutions that suit your specific needs'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href={`/${locale}/contact`}
                className="bg-white text-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center gap-2"
              >
                <i className="ri-phone-line"></i>
                {currentContent.cta}
              </a>
              <a
                href={`https://wa.me/${whatsappNumber.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(locale === 'ar' ? 'مرحباً، أريد الاستفسار عن خدماتكم' : 'Hello, I would like to inquire about your services')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-3 rounded-lg font-semibold transition-colors inline-flex items-center justify-center gap-2"
              >
                <i className="ri-whatsapp-line"></i>
                {locale === 'ar' ? 'واتساب' : 'WhatsApp'}
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
