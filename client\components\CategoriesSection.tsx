'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { Locale } from '../lib/i18n';
import { Category } from '../src/types/database';
import { categoriesApi } from '../src/lib/api';

interface CategoriesSectionProps {
  locale: Locale;
}

const CategoriesSection: React.FC<CategoriesSectionProps> = ({ locale }) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [visibleCategories, setVisibleCategories] = useState<Set<number>>(new Set());
  const observerRef = useRef<IntersectionObserver | null>(null);
  const categoryRefs = useRef<(HTMLElement | null)[]>([]);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const data = await categoriesApi.getAll();
        setCategories(data.filter(cat => cat.isActive));
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // إعداد Intersection Observer للأنيميشن
  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleCategories(prev => new Set([...prev, index]));
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  // مراقبة العناصر عند تحميل الفئات
  useEffect(() => {
    if (!loading && categories.length > 0 && observerRef.current) {
      categoryRefs.current.forEach((ref) => {
        if (ref) {
          observerRef.current?.observe(ref);
        }
      });
    }

    return () => {
      if (observerRef.current) {
        categoryRefs.current.forEach((ref) => {
          if (ref) {
            observerRef.current?.unobserve(ref);
          }
        });
      }
    };
  }, [loading, categories]);

  if (loading) {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/20 border-t-primary mx-auto mb-6"></div>
              <div className="absolute inset-0 rounded-full h-16 w-16 border-4 border-transparent border-r-primary/40 mx-auto animate-pulse"></div>
            </div>
            <p className="text-gray-700 text-lg font-medium animate-pulse">
              {locale === 'ar' ? 'جاري تحميل الفئات...' : 'Loading categories...'}
            </p>
            <div className="mt-4 flex justify-center space-x-2">
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 relative overflow-hidden">
      {/* خلفية ديكورية */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-32 h-32 bg-primary rounded-full animate-float"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-purple-400 rounded-full animate-float" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-20 left-1/4 w-20 h-20 bg-blue-400 rounded-full animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-40 right-1/3 w-16 h-16 bg-green-400 rounded-full animate-float" style={{animationDelay: '0.5s'}}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16 animate-fadeInUp">
          <div className="inline-block mb-6">
            <span className="bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent text-sm font-bold uppercase tracking-wider">
              {locale === 'ar' ? 'تصفح مجموعاتنا' : 'Browse Our Collections'}
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 bg-clip-text text-transparent mb-6 leading-tight">
            {locale === 'ar' ? 'فئات المنتجات' : 'Product Categories'}
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-primary to-purple-600 mx-auto mb-6 rounded-full"></div>
          <p className="text-gray-600 text-lg md:text-xl max-w-3xl mx-auto leading-relaxed">
            {locale === 'ar'
              ? 'اكتشف مجموعتنا الواسعة من المعدات المصنفة حسب الاستخدام والمصممة خصيصاً لتلبية احتياجات المطاعم والفنادق'
              : 'Discover our wide range of equipment categorized by usage and specially designed to meet restaurant and hotel needs'
            }
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {categories.map((category, index) => (
            <div
              key={category.id}
              ref={(el) => {categoryRefs.current[index] = el}}
              data-index={index}
              className={`category-card bg-white rounded-2xl shadow-xl overflow-hidden transform transition-all duration-700 ${
                visibleCategories.has(index)
                  ? 'animate-category-slide-in opacity-100'
                  : 'opacity-0 translate-y-8'
              }`}
              style={{
                animationDelay: `${index * 0.1}s`
              }}
            >
              <Link
                href={`/${locale}/category/${category.id}`}
                className="block group"
              >
                <div className="relative h-56 overflow-hidden">
                  <img
                    src={category.image || 'https://via.placeholder.com/400x300'}
                    alt={locale === 'ar' ? category.nameAr : category.name}
                    className="category-image w-full h-full object-cover"
                  />
                  <div className="category-overlay absolute inset-0"></div>

                  {/* شارة الفئة */}
                  <div className="absolute top-4 right-4 bg-white/95 backdrop-blur-sm rounded-full p-3 shadow-lg z-10">
                    <i className="ri-store-2-line text-primary text-lg animate-icon-bounce"></i>
                  </div>

                  {/* عداد المنتجات فقط */}
                  <div className="absolute bottom-4 right-4 z-20">
                    <div className="bg-white/25 backdrop-blur-sm rounded-full px-3 py-1 border border-white/20">
                      <span className="text-white text-xs font-semibold drop-shadow-sm">
                        {locale === 'ar' ? 'متاح' : 'Available'} +
                      </span>
                    </div>
                  </div>

                  {/* تأثير التمرير */}
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-purple-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                <div className="category-footer p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="text-gray-800 font-bold text-xl mb-1">
                        {locale === 'ar' ? category.nameAr : category.name}
                      </h3>
                      <p className="text-gray-600 text-sm line-clamp-2">
                        {locale === 'ar' ? category.descriptionAr : category.description}
                      </p>
                    </div>
                    <div className="category-arrow bg-primary/10 rounded-full p-3 group-hover:bg-primary group-hover:text-white transition-all duration-300 ml-4">
                      <i className="ri-arrow-right-line text-primary group-hover:text-white text-lg"></i>
                    </div>
                  </div>

                  {/* شريط التقدم الديكوري */}
                  <div className="mt-4 h-1 bg-gray-200 rounded-full overflow-hidden">
                    <div className="h-full bg-gradient-to-r from-primary to-purple-600 rounded-full transform -translate-x-full group-hover:translate-x-0 transition-transform duration-500"></div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>

        {/* قسم إضافي للتشجيع على الاستكشاف */}
        <div className="mt-20 text-center animate-fadeInUp">
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-white/20 max-w-4xl mx-auto">
            <div className="flex items-center justify-center mb-6">
              <div className="bg-gradient-to-r from-primary to-purple-600 rounded-full p-4 animate-pulse">
                <i className="ri-search-line text-white text-2xl"></i>
              </div>
            </div>
            <h3 className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
              {locale === 'ar' ? 'لم تجد ما تبحث عنه؟' : "Can't find what you're looking for?"}
            </h3>
            <p className="text-gray-600 text-lg mb-6 max-w-2xl mx-auto">
              {locale === 'ar'
                ? 'تواصل معنا للحصول على استشارة مجانية وحلول مخصصة لاحتياجاتك'
                : 'Contact us for free consultation and customized solutions for your needs'
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-gradient-to-r from-primary to-purple-600 hover:from-purple-600 hover:to-primary text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl animate-gradient-shift">
                <i className="ri-phone-line ml-2"></i>
                {locale === 'ar' ? 'اتصل بنا' : 'Call Us'}
              </button>
              <button className="bg-white hover:bg-gray-50 text-primary border-2 border-primary px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-xl">
                <i className="ri-mail-line ml-2"></i>
                {locale === 'ar' ? 'راسلنا' : 'Email Us'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CategoriesSection;
