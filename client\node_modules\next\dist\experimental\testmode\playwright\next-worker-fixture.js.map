{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/next-worker-fixture.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON>lerR<PERSON>ult, ProxyServer } from '../proxy'\nimport { createProxyServer } from '../proxy'\n\nexport type FetchHandler = (\n  request: Request\n) => FetchHandlerResult | Promise<FetchHandlerResult>\n\nexport interface NextWorkerFixture {\n  proxyPort: number\n  onFetch: (testId: string, handler: <PERSON>tch<PERSON><PERSON><PERSON>) => void\n  cleanupTest: (testId: string) => void\n}\n\nclass NextWorkerFixtureImpl implements NextWorkerFixture {\n  public proxyPort: number = 0\n  private proxyServer: ProxyServer | null = null\n  private proxyFetchMap = new Map<string, FetchHandler>()\n\n  async setup(): Promise<void> {\n    const server = await createProxyServer({\n      onFetch: this.handleProxyFetch.bind(this),\n    })\n\n    this.proxyPort = server.port\n    this.proxyServer = server\n  }\n\n  teardown(): void {\n    if (this.proxyServer) {\n      this.proxyServer.close()\n      this.proxyServer = null\n    }\n  }\n\n  cleanupTest(testId: string): void {\n    this.proxyFetchMap.delete(testId)\n  }\n\n  onFetch(testId: string, handler: <PERSON><PERSON><PERSON><PERSON><PERSON>): void {\n    this.proxyFetchMap.set(testId, handler)\n  }\n\n  private async handleProxyFetch(\n    testId: string,\n    request: Request\n  ): Promise<FetchHandlerResult> {\n    const handler = this.proxyFetchMap.get(testId)\n    return handler?.(request)\n  }\n}\n\nexport async function applyNextWorkerFixture(\n  use: (fixture: NextWorkerFixture) => Promise<void>\n): Promise<void> {\n  const fixture = new NextWorkerFixtureImpl()\n  await fixture.setup()\n  // eslint-disable-next-line react-hooks/rules-of-hooks -- not React.use()\n  await use(fixture)\n  fixture.teardown()\n}\n"], "names": ["applyNextWorkerFixture", "NextWorkerFixtureImpl", "setup", "server", "createProxyServer", "onFetch", "handleProxyFetch", "bind", "proxyPort", "port", "proxyServer", "teardown", "close", "cleanupTest", "testId", "proxyFetchMap", "delete", "handler", "set", "request", "get", "Map", "use", "fixture"], "mappings": ";;;;+BAmDsBA;;;eAAAA;;;uBAlDY;AAYlC,MAAMC;IAKJ,MAAMC,QAAuB;QAC3B,MAAMC,SAAS,MAAMC,IAAAA,wBAAiB,EAAC;YACrCC,SAAS,IAAI,CAACC,gBAAgB,CAACC,IAAI,CAAC,IAAI;QAC1C;QAEA,IAAI,CAACC,SAAS,GAAGL,OAAOM,IAAI;QAC5B,IAAI,CAACC,WAAW,GAAGP;IACrB;IAEAQ,WAAiB;QACf,IAAI,IAAI,CAACD,WAAW,EAAE;YACpB,IAAI,CAACA,WAAW,CAACE,KAAK;YACtB,IAAI,CAACF,WAAW,GAAG;QACrB;IACF;IAEAG,YAAYC,MAAc,EAAQ;QAChC,IAAI,CAACC,aAAa,CAACC,MAAM,CAACF;IAC5B;IAEAT,QAAQS,MAAc,EAAEG,OAAqB,EAAQ;QACnD,IAAI,CAACF,aAAa,CAACG,GAAG,CAACJ,QAAQG;IACjC;IAEA,MAAcX,iBACZQ,MAAc,EACdK,OAAgB,EACa;QAC7B,MAAMF,UAAU,IAAI,CAACF,aAAa,CAACK,GAAG,CAACN;QACvC,OAAOG,2BAAAA,QAAUE;IACnB;;aAlCOX,YAAoB;aACnBE,cAAkC;aAClCK,gBAAgB,IAAIM;;AAiC9B;AAEO,eAAerB,uBACpBsB,GAAkD;IAElD,MAAMC,UAAU,IAAItB;IACpB,MAAMsB,QAAQrB,KAAK;IACnB,yEAAyE;IACzE,MAAMoB,IAAIC;IACVA,QAAQZ,QAAQ;AAClB"}