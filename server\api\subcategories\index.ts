import { NextApiRequest, NextApiResponse } from 'next';
import { getSubcategories, getSubcategoriesByCategory, addSubcategory } from '../../../utils/database';
import { Subcategory } from '../../../types/admin';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        const { categoryId } = req.query;
        
        let subcategories: Subcategory[];
        if (categoryId && typeof categoryId === 'string') {
          subcategories = getSubcategoriesByCategory(categoryId);
        } else {
          subcategories = getSubcategories();
        }
        
        res.status(200).json(subcategories);
        break;

      case 'POST':
        const newSubcategoryData = req.body as Omit<Subcategory, 'id' | 'createdAt' | 'updatedAt'>;
        const newSubcategory = addSubcategory(newSubcategoryData);
        res.status(201).json(newSubcategory);
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
