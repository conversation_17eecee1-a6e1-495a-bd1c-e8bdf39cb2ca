{"version": 3, "sources": ["../../../../src/experimental/testmode/proxy/server.ts"], "sourcesContent": ["import http from 'http'\nimport type { IncomingMessage } from 'http'\nimport type { ProxyRequest, ProxyResponse, ProxyServer } from './types'\nimport { UNHANDLED } from './types'\nimport type { <PERSON>tch<PERSON>andler } from './fetch-api'\nimport { handleFetch } from './fetch-api'\n\nasync function readBody(req: IncomingMessage): Promise<Buffer> {\n  const acc: Buffer[] = []\n\n  for await (const chunk of req) {\n    acc.push(chunk)\n  }\n\n  return Buffer.concat(acc)\n}\n\nexport async function createProxyServer({\n  onFetch,\n}: {\n  onFetch?: FetchHandler\n}): Promise<ProxyServer> {\n  const server = http.createServer(async (req, res) => {\n    if (req.url !== '/') {\n      res.writeHead(404)\n      res.end()\n      return\n    }\n\n    let json: ProxyRequest | undefined\n    try {\n      json = JSON.parse((await readBody(req)).toString('utf-8')) as ProxyRequest\n    } catch (e) {\n      res.writeHead(400)\n      res.end()\n      return\n    }\n\n    const { api } = json\n\n    let response: ProxyResponse | undefined\n    switch (api) {\n      case 'fetch':\n        if (onFetch) {\n          response = await handleFetch(json, onFetch)\n        }\n        break\n      default:\n        break\n    }\n    if (!response) {\n      response = UNHANDLED\n    }\n\n    res.writeHead(200, { 'Content-Type': 'application/json' })\n    res.write(JSON.stringify(response))\n    res.end()\n  })\n\n  await new Promise((resolve) => {\n    server.listen(0, '::', () => {\n      resolve(undefined)\n    })\n  })\n\n  const address = server.address()\n  if (!address || typeof address !== 'object') {\n    server.close()\n    throw new Error('Failed to create a proxy server')\n  }\n  const port = address.port\n\n  const fetchWith: ProxyServer['fetchWith'] = (input, init, testData) => {\n    const request = new Request(input, init)\n    request.headers.set('Next-Test-Proxy-Port', String(port))\n    request.headers.set('Next-Test-Data', testData ?? '')\n    return fetch(request)\n  }\n\n  return { port, close: () => server.close(), fetchWith }\n}\n"], "names": ["createProxyServer", "readBody", "req", "acc", "chunk", "push", "<PERSON><PERSON><PERSON>", "concat", "onFetch", "server", "http", "createServer", "res", "url", "writeHead", "end", "json", "JSON", "parse", "toString", "e", "api", "response", "handleFetch", "UNHANDLED", "write", "stringify", "Promise", "resolve", "listen", "undefined", "address", "close", "Error", "port", "fetchWith", "input", "init", "testData", "request", "Request", "headers", "set", "String", "fetch"], "mappings": ";;;;+BAiBsBA;;;eAAAA;;;6DAjBL;uBAGS;0BAEE;;;;;;AAE5B,eAAeC,SAASC,GAAoB;IAC1C,MAAMC,MAAgB,EAAE;IAExB,WAAW,MAAMC,SAASF,IAAK;QAC7BC,IAAIE,IAAI,CAACD;IACX;IAEA,OAAOE,OAAOC,MAAM,CAACJ;AACvB;AAEO,eAAeH,kBAAkB,EACtCQ,OAAO,EAGR;IACC,MAAMC,SAASC,aAAI,CAACC,YAAY,CAAC,OAAOT,KAAKU;QAC3C,IAAIV,IAAIW,GAAG,KAAK,KAAK;YACnBD,IAAIE,SAAS,CAAC;YACdF,IAAIG,GAAG;YACP;QACF;QAEA,IAAIC;QACJ,IAAI;YACFA,OAAOC,KAAKC,KAAK,CAAC,AAAC,CAAA,MAAMjB,SAASC,IAAG,EAAGiB,QAAQ,CAAC;QACnD,EAAE,OAAOC,GAAG;YACVR,IAAIE,SAAS,CAAC;YACdF,IAAIG,GAAG;YACP;QACF;QAEA,MAAM,EAAEM,GAAG,EAAE,GAAGL;QAEhB,IAAIM;QACJ,OAAQD;YACN,KAAK;gBACH,IAAIb,SAAS;oBACXc,WAAW,MAAMC,IAAAA,qBAAW,EAACP,MAAMR;gBACrC;gBACA;YACF;gBACE;QACJ;QACA,IAAI,CAACc,UAAU;YACbA,WAAWE,gBAAS;QACtB;QAEAZ,IAAIE,SAAS,CAAC,KAAK;YAAE,gBAAgB;QAAmB;QACxDF,IAAIa,KAAK,CAACR,KAAKS,SAAS,CAACJ;QACzBV,IAAIG,GAAG;IACT;IAEA,MAAM,IAAIY,QAAQ,CAACC;QACjBnB,OAAOoB,MAAM,CAAC,GAAG,MAAM;YACrBD,QAAQE;QACV;IACF;IAEA,MAAMC,UAAUtB,OAAOsB,OAAO;IAC9B,IAAI,CAACA,WAAW,OAAOA,YAAY,UAAU;QAC3CtB,OAAOuB,KAAK;QACZ,MAAM,qBAA4C,CAA5C,IAAIC,MAAM,oCAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA2C;IACnD;IACA,MAAMC,OAAOH,QAAQG,IAAI;IAEzB,MAAMC,YAAsC,CAACC,OAAOC,MAAMC;QACxD,MAAMC,UAAU,IAAIC,QAAQJ,OAAOC;QACnCE,QAAQE,OAAO,CAACC,GAAG,CAAC,wBAAwBC,OAAOT;QACnDK,QAAQE,OAAO,CAACC,GAAG,CAAC,kBAAkBJ,YAAY;QAClD,OAAOM,MAAML;IACf;IAEA,OAAO;QAAEL;QAAMF,OAAO,IAAMvB,OAAOuB,KAAK;QAAIG;IAAU;AACxD"}