import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: any[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  if (req.method === 'PATCH') {
    try {
      const { status } = req.body;

      const quotesDir = path.join(process.cwd(), 'src', 'data', 'quotes');
      const jsonFilePath = path.join(quotesDir, `${id}.json`);
      const indexFilePath = path.join(quotesDir, 'index.json');

      // التحقق من وجود الطلب
      if (!fs.existsSync(jsonFilePath)) {
        return res.status(404).json({
          success: false,
          message: 'الطلب غير موجود'
        });
      }

      // قراءة وتحديث بيانات الطلب
      const requestContent = fs.readFileSync(jsonFilePath, 'utf-8');
      const quoteRequest: QuoteRequest = JSON.parse(requestContent);
      
      quoteRequest.status = status;
      
      // حفظ التحديث
      fs.writeFileSync(jsonFilePath, JSON.stringify(quoteRequest, null, 2));

      // تحديث ملف الفهرس
      if (fs.existsSync(indexFilePath)) {
        const indexContent = fs.readFileSync(indexFilePath, 'utf-8');
        const allRequests: QuoteRequest[] = JSON.parse(indexContent);
        
        const requestIndex = allRequests.findIndex(req => req.id === id);
        if (requestIndex !== -1) {
          allRequests[requestIndex] = quoteRequest;
          fs.writeFileSync(indexFilePath, JSON.stringify(allRequests, null, 2));
        }
      }

      res.status(200).json({
        success: true,
        message: 'تم تحديث حالة الطلب بنجاح',
        request: quoteRequest
      });

    } catch (error) {
      console.error('Error updating quote request:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء تحديث الطلب'
      });
    }
  } else if (req.method === 'GET') {
    try {
      const quotesDir = path.join(process.cwd(), 'src', 'data', 'quotes');
      const jsonFilePath = path.join(quotesDir, `${id}.json`);

      if (!fs.existsSync(jsonFilePath)) {
        return res.status(404).json({
          success: false,
          message: 'الطلب غير موجود'
        });
      }

      const requestContent = fs.readFileSync(jsonFilePath, 'utf-8');
      const quoteRequest = JSON.parse(requestContent);

      res.status(200).json({
        success: true,
        request: quoteRequest
      });

    } catch (error) {
      console.error('Error fetching quote request:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء جلب الطلب'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'PATCH']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
