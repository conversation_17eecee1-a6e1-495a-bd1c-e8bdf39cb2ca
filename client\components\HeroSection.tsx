import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Locale } from '../lib/i18n';
import { useSiteSettings } from '../src/hooks/useSiteSettings';

interface HeroSectionProps {
  locale: Locale;
}

const HeroSection: React.FC<HeroSectionProps> = ({ locale }) => {
  const { settings, loading } = useSiteSettings();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [whatsappNumber, setWhatsappNumber] = useState('+966501234567');

  // الصور الافتراضية في حالة عدم وجود إعدادات
  const defaultImages = [
    'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=1200&h=800&fit=crop',
    'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=1200&h=800&fit=crop',
    'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=1200&h=800&fit=crop'
  ];

  // استخدام الصور من الإعدادات أو الصور الافتراضية
  const heroImages = settings?.heroImages?.filter(img => img.trim() !== '') || defaultImages;

  // تغيير الصورة كل 5 ثوانٍ
  useEffect(() => {
    if (heroImages.length > 1) {
      const interval = setInterval(() => {
        setCurrentImageIndex((prevIndex) =>
          (prevIndex + 1) % heroImages.length
        );
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [heroImages.length]);

  // قراءة رقم الواتساب من الإعدادات
  useEffect(() => {
    // استخدام فقط إعدادات التواصل الجديدة، تجاهل الرقم القديم
    if (settings?.communicationSettings?.whatsapp?.businessNumber) {
      setWhatsappNumber(settings.communicationSettings.whatsapp.businessNumber);
    }
  }, [settings]);

  // عرض حالة التحميل
  if (loading) {
    return (
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gray-900">
        <div className="text-center text-white">
          <i className="ri-loader-4-line text-4xl animate-spin mb-4"></i>
          <p>{locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}</p>
        </div>
      </section>
    );
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Images Slider */}
      <div className="absolute inset-0 z-0">
        {heroImages.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentImageIndex ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <img
              src={image}
              alt={`Hero Image ${index + 1}`}
              className="w-full h-full object-cover"
              onError={(e) => {
                (e.target as HTMLImageElement).src = defaultImages[0];
              }}
            />
          </div>
        ))}
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70"></div>
      </div>

      {/* Slider Indicators */}
      {heroImages.length > 1 && (
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 flex gap-2 z-20">
          {heroImages.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentImageIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentImageIndex
                  ? 'bg-white scale-110'
                  : 'bg-white/50 hover:bg-white/70'
              }`}
              aria-label={`${locale === 'ar' ? 'الانتقال للصورة' : 'Go to image'} ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Floating Elements */}
      <div className="absolute inset-0 z-5">
        <div className="absolute top-20 left-10 w-20 h-20 bg-primary/20 rounded-full animate-pulse"></div>
        <div className="absolute top-40 right-20 w-16 h-16 bg-white/10 rounded-full animate-bounce" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-40 left-20 w-12 h-12 bg-primary/30 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center text-white px-4 max-w-5xl mx-auto">
        {/* Badge */}
        <div className="inline-flex items-center gap-2 bg-primary/20 backdrop-blur-sm border border-primary/30 rounded-full px-6 py-2 mb-6 animate-fadeInUp">
          <i className="ri-award-line text-primary"></i>
          <span className="text-sm font-medium">
            {locale === 'ar' ? '15+ سنة من التميز' : '15+ Years of Excellence'}
          </span>
        </div>

        <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-fadeInUp leading-tight">
          {locale === 'ar'
            ? 'معدات الضيافة عالية الجودة'
            : 'High-Quality Hospitality Equipment'
          }
        </h1>

        <p className="text-xl md:text-2xl mb-4 text-gray-200 animate-fadeInUp max-w-3xl mx-auto leading-relaxed" style={{ animationDelay: '0.2s' }}>
          {locale === 'ar'
            ? 'نوفر أفضل المعدات للمطاعم والفنادق والمقاهي مع ضمان الجودة والخدمة المتميزة'
            : 'We provide the best equipment for restaurants, hotels, and cafes with quality assurance and excellent service'
          }
        </p>

        {/* Features */}
        <div className="flex flex-wrap justify-center gap-6 mb-8 animate-fadeInUp" style={{ animationDelay: '0.3s' }}>
          <div className="flex items-center gap-2 text-sm">
            <i className="ri-check-line text-green-400"></i>
            <span>{locale === 'ar' ? 'ضمان 5 سنوات' : '5-Year Warranty'}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <i className="ri-check-line text-green-400"></i>
            <span>{locale === 'ar' ? 'توصيل مجاني' : 'Free Delivery'}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <i className="ri-check-line text-green-400"></i>
            <span>{locale === 'ar' ? 'دعم فني 24/7' : '24/7 Support'}</span>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fadeInUp" style={{ animationDelay: '0.4s' }}>
          <Link
            href={`/${locale}/products`}
            className="group bg-primary hover:bg-primary/90 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl flex items-center justify-center gap-2"
          >
            <i className="ri-shopping-bag-line"></i>
            {locale === 'ar' ? 'تصفح المنتجات' : 'Browse Products'}
            <i className="ri-arrow-right-line group-hover:translate-x-1 transition-transform"></i>
          </Link>
          <Link
            href={`/${locale}/contact`}
            className="group border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 backdrop-blur-sm flex items-center justify-center gap-2"
          >
            <i className="ri-phone-line"></i>
            {locale === 'ar' ? 'تواصل معنا' : 'Contact Us'}
          </Link>
          <a
            href={`https://wa.me/${whatsappNumber.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(locale === 'ar' ? 'مرحباً، أريد الاستفسار عن منتجاتكم' : 'Hello, I would like to inquire about your products')}`}
            target="_blank"
            rel="noopener noreferrer"
            className="group bg-green-600 hover:bg-green-700 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl flex items-center justify-center gap-2"
          >
            <i className="ri-whatsapp-line"></i>
            {locale === 'ar' ? 'واتساب' : 'WhatsApp'}
          </a>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <div className="flex flex-col items-center gap-2">
          <span className="text-sm">{locale === 'ar' ? 'اكتشف المزيد' : 'Discover More'}</span>
          <i className="ri-arrow-down-line text-2xl"></i>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
