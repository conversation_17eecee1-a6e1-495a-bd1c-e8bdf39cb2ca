import { NextApiRequest, NextApiResponse } from 'next';
import { searchProducts } from '../../../lib/database';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        const { q } = req.query;
        
        if (!q || typeof q !== 'string') {
          return res.status(400).json({ error: 'Search query is required' });
        }
        
        const products = searchProducts(q);
        res.status(200).json(products);
        break;

      default:
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
