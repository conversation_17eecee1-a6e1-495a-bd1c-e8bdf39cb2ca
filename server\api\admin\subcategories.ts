import { NextApiRequest, NextApiResponse } from 'next';
import { 
  getSubcategories, 
  addSubcategory, 
  updateSubcategory, 
  deleteSubcategory, 
  getSubcategoryById,
  getSubcategoriesByCategory 
} from '../../../utils/database';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        const { categoryId } = req.query;
        
        let subcategories;
        if (categoryId && typeof categoryId === 'string') {
          subcategories = getSubcategoriesByCategory(categoryId);
        } else {
          subcategories = getSubcategories();
        }
        
        res.status(200).json({ success: true, data: subcategories });
        break;

      case 'POST':
        const { name, nameAr, categoryId: catId, description, descriptionAr, image, isActive } = req.body;
        
        if (!name || !nameAr || !catId) {
          return res.status(400).json({ 
            success: false, 
            message: 'Name, Arabic name, and category ID are required' 
          });
        }

        const newSubcategory = addSubcategory({
          name,
          nameAr,
          categoryId: catId,
          description: description || '',
          descriptionAr: descriptionAr || '',
          image: image || '',
          isActive: isActive !== undefined ? isActive : true
        });

        res.status(201).json({ success: true, data: newSubcategory });
        break;

      case 'PUT':
        const { id } = req.query;
        
        if (!id || typeof id !== 'string') {
          return res.status(400).json({ 
            success: false, 
            message: 'Subcategory ID is required' 
          });
        }

        const existingSubcategory = getSubcategoryById(id);
        if (!existingSubcategory) {
          return res.status(404).json({ 
            success: false, 
            message: 'Subcategory not found' 
          });
        }

        const updatedSubcategory = updateSubcategory(id, req.body);
        res.status(200).json({ success: true, data: updatedSubcategory });
        break;

      case 'DELETE':
        const { id: deleteId } = req.query;
        
        if (!deleteId || typeof deleteId !== 'string') {
          return res.status(400).json({ 
            success: false, 
            message: 'Subcategory ID is required' 
          });
        }

        const deleted = deleteSubcategory(deleteId);
        
        if (!deleted) {
          return res.status(404).json({ 
            success: false, 
            message: 'Subcategory not found' 
          });
        }

        res.status(200).json({ 
          success: true, 
          message: 'Subcategory deleted successfully' 
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        res.status(405).json({ 
          success: false, 
          message: `Method ${req.method} not allowed` 
        });
    }
  } catch (error) {
    console.error('Subcategories API error:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
}
