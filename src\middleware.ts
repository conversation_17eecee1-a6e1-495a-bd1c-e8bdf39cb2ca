import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// هذا الملف يعمل كوسيط لجميع الطلبات
export function middleware(request: NextRequest) {
  // يمكن إضافة منطق التحقق من المصادقة هنا
  // أو إعادة توجيه المستخدم إلى صفحات معينة
  
  // مثال: إضافة رأس أمان
  const response = NextResponse.next();
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  return response;
}

// تكوين المسارات التي سيتم تطبيق الوسيط عليها
export const config = {
  matcher: [
    // تطبيق على جميع المسارات باستثناء المسارات المستثناة
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
