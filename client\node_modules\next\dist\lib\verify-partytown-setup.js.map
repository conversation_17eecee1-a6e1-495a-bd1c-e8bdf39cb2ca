{"version": 3, "sources": ["../../src/lib/verify-partytown-setup.ts"], "sourcesContent": ["import { promises } from 'fs'\nimport { bold, cyan, red } from './picocolors'\n\nimport path from 'path'\nimport { hasNecessaryDependencies } from './has-necessary-dependencies'\nimport type { NecessaryDependencies } from './has-necessary-dependencies'\nimport { fileExists, FileType } from './file-exists'\nimport { FatalError } from './fatal-error'\nimport * as Log from '../build/output/log'\nimport { getPkgManager } from './helpers/get-pkg-manager'\n\nasync function missingDependencyError(dir: string) {\n  const packageManager = getPkgManager(dir)\n\n  throw new FatalError(\n    bold(\n      red(\n        \"It looks like you're trying to use Partytown with next/script but do not have the required package(s) installed.\"\n      )\n    ) +\n      '\\n\\n' +\n      bold(`Please install Partytown by running:`) +\n      '\\n\\n' +\n      `\\t${bold(\n        cyan(\n          (packageManager === 'yarn'\n            ? 'yarn add --dev'\n            : packageManager === 'pnpm'\n              ? 'pnpm install --save-dev'\n              : 'npm install --save-dev') + ' @builder.io/partytown'\n        )\n      )}` +\n      '\\n\\n' +\n      bold(\n        `If you are not trying to use Partytown, please disable the experimental ${cyan(\n          '\"nextScriptWorkers\"'\n        )} flag in next.config.js.`\n      ) +\n      '\\n'\n  )\n}\n\nasync function copyPartytownStaticFiles(\n  deps: NecessaryDependencies,\n  staticDir: string\n) {\n  const partytownLibDir = path.join(staticDir, '~partytown')\n  const hasPartytownLibDir = await fileExists(\n    partytownLibDir,\n    FileType.Directory\n  )\n\n  if (hasPartytownLibDir) {\n    await promises.rm(partytownLibDir, { recursive: true, force: true })\n  }\n\n  const { copyLibFiles } = await Promise.resolve(\n    require(path.join(deps.resolved.get('@builder.io/partytown')!, '../utils'))\n  )\n\n  await copyLibFiles(partytownLibDir)\n}\n\nexport async function verifyPartytownSetup(\n  dir: string,\n  targetDir: string\n): Promise<void> {\n  try {\n    const partytownDeps: NecessaryDependencies = await hasNecessaryDependencies(\n      dir,\n      [\n        {\n          file: '@builder.io/partytown',\n          pkg: '@builder.io/partytown',\n          exportsRestrict: false,\n        },\n      ]\n    )\n\n    if (partytownDeps.missing?.length > 0) {\n      await missingDependencyError(dir)\n    } else {\n      try {\n        await copyPartytownStaticFiles(partytownDeps, targetDir)\n      } catch (err) {\n        Log.warn(\n          `Partytown library files could not be copied to the static directory. Please ensure that ${bold(\n            cyan('@builder.io/partytown')\n          )} is installed as a dependency.`\n        )\n      }\n    }\n  } catch (err) {\n    // Don't show a stack trace when there is an error due to missing dependencies\n    if (err instanceof FatalError) {\n      console.error(err.message)\n      process.exit(1)\n    }\n    throw err\n  }\n}\n"], "names": ["verifyPartytownSetup", "missingDependencyError", "dir", "packageManager", "getPkgManager", "FatalE<PERSON>r", "bold", "red", "cyan", "copyPartytownStaticFiles", "deps", "staticDir", "partytownLibDir", "path", "join", "hasPartytownLibDir", "fileExists", "FileType", "Directory", "promises", "rm", "recursive", "force", "copyLibFiles", "Promise", "resolve", "require", "resolved", "get", "targetDir", "partytownDeps", "hasNecessaryDependencies", "file", "pkg", "exportsRestrict", "missing", "length", "err", "Log", "warn", "console", "error", "message", "process", "exit"], "mappings": ";;;;+BA+DsBA;;;eAAAA;;;oBA/DG;4BACO;6DAEf;0CACwB;4BAEJ;4BACV;6DACN;+BACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9B,eAAeC,uBAAuBC,GAAW;IAC/C,MAAMC,iBAAiBC,IAAAA,4BAAa,EAACF;IAErC,MAAM,qBAyBL,CAzBK,IAAIG,sBAAU,CAClBC,IAAAA,gBAAI,EACFC,IAAAA,eAAG,EACD,uHAGF,SACAD,IAAAA,gBAAI,EAAC,CAAC,oCAAoC,CAAC,IAC3C,SACA,CAAC,EAAE,EAAEA,IAAAA,gBAAI,EACPE,IAAAA,gBAAI,EACF,AAACL,CAAAA,mBAAmB,SAChB,mBACAA,mBAAmB,SACjB,4BACA,wBAAuB,IAAK,4BAEnC,GACH,SACAG,IAAAA,gBAAI,EACF,CAAC,wEAAwE,EAAEE,IAAAA,gBAAI,EAC7E,uBACA,wBAAwB,CAAC,IAE7B,OAxBE,qBAAA;eAAA;oBAAA;sBAAA;IAyBN;AACF;AAEA,eAAeC,yBACbC,IAA2B,EAC3BC,SAAiB;IAEjB,MAAMC,kBAAkBC,aAAI,CAACC,IAAI,CAACH,WAAW;IAC7C,MAAMI,qBAAqB,MAAMC,IAAAA,sBAAU,EACzCJ,iBACAK,oBAAQ,CAACC,SAAS;IAGpB,IAAIH,oBAAoB;QACtB,MAAMI,YAAQ,CAACC,EAAE,CAACR,iBAAiB;YAAES,WAAW;YAAMC,OAAO;QAAK;IACpE;IAEA,MAAM,EAAEC,YAAY,EAAE,GAAG,MAAMC,QAAQC,OAAO,CAC5CC,QAAQb,aAAI,CAACC,IAAI,CAACJ,KAAKiB,QAAQ,CAACC,GAAG,CAAC,0BAA2B;IAGjE,MAAML,aAAaX;AACrB;AAEO,eAAeZ,qBACpBE,GAAW,EACX2B,SAAiB;IAEjB,IAAI;YAYEC;QAXJ,MAAMA,gBAAuC,MAAMC,IAAAA,kDAAwB,EACzE7B,KACA;YACE;gBACE8B,MAAM;gBACNC,KAAK;gBACLC,iBAAiB;YACnB;SACD;QAGH,IAAIJ,EAAAA,yBAAAA,cAAcK,OAAO,qBAArBL,uBAAuBM,MAAM,IAAG,GAAG;YACrC,MAAMnC,uBAAuBC;QAC/B,OAAO;YACL,IAAI;gBACF,MAAMO,yBAAyBqB,eAAeD;YAChD,EAAE,OAAOQ,KAAK;gBACZC,KAAIC,IAAI,CACN,CAAC,wFAAwF,EAAEjC,IAAAA,gBAAI,EAC7FE,IAAAA,gBAAI,EAAC,0BACL,8BAA8B,CAAC;YAErC;QACF;IACF,EAAE,OAAO6B,KAAK;QACZ,8EAA8E;QAC9E,IAAIA,eAAehC,sBAAU,EAAE;YAC7BmC,QAAQC,KAAK,CAACJ,IAAIK,OAAO;YACzBC,QAAQC,IAAI,CAAC;QACf;QACA,MAAMP;IACR;AACF"}