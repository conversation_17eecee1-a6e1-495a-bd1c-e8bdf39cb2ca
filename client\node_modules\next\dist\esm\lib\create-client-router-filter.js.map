{"version": 3, "sources": ["../../src/lib/create-client-router-filter.ts"], "sourcesContent": ["import type { Token } from 'next/dist/compiled/path-to-regexp'\nimport { BloomFilter } from '../shared/lib/bloom-filter'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport type { Redirect } from './load-custom-routes'\nimport { tryToParsePath } from './try-to-parse-path'\nimport {\n  extractInterceptionRouteInformation,\n  isInterceptionRouteAppPath,\n} from '../shared/lib/router/utils/interception-routes'\n\nexport function createClientRouterFilter(\n  paths: string[],\n  redirects: Redirect[],\n  allowedErrorRate?: number\n): {\n  staticFilter: ReturnType<BloomFilter['export']>\n  dynamicFilter: ReturnType<BloomFilter['export']>\n} {\n  const staticPaths = new Set<string>()\n  const dynamicPaths = new Set<string>()\n\n  for (let path of paths) {\n    if (isDynamicRoute(path)) {\n      if (isInterceptionRouteAppPath(path)) {\n        path = extractInterceptionRouteInformation(path).interceptedRoute\n      }\n\n      let subPath = ''\n      const pathParts = path.split('/')\n\n      // start at 1 since we split on '/' and the path starts\n      // with this so the first entry is an empty string\n      for (let i = 1; i < pathParts.length; i++) {\n        const curPart = pathParts[i]\n\n        if (curPart.startsWith('[')) {\n          break\n        }\n        subPath = `${subPath}/${curPart}`\n      }\n\n      if (subPath) {\n        dynamicPaths.add(subPath)\n      }\n    } else {\n      staticPaths.add(path)\n    }\n  }\n\n  for (const redirect of redirects) {\n    const { source } = redirect\n    const path = removeTrailingSlash(source)\n    let tokens: Token[] = []\n\n    try {\n      tokens = tryToParsePath(source).tokens || []\n    } catch {}\n\n    if (tokens.every((token) => typeof token === 'string')) {\n      // only include static redirects initially\n      staticPaths.add(path)\n    }\n  }\n\n  const staticFilter = BloomFilter.from([...staticPaths], allowedErrorRate)\n\n  const dynamicFilter = BloomFilter.from([...dynamicPaths], allowedErrorRate)\n  const data = {\n    staticFilter: staticFilter.export(),\n    dynamicFilter: dynamicFilter.export(),\n  }\n  return data\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "isDynamicRoute", "removeTrailingSlash", "tryToParsePath", "extractInterceptionRouteInformation", "isInterceptionRouteAppPath", "createClientRouterFilter", "paths", "redirects", "allowedErrorRate", "staticPaths", "Set", "dynamicPaths", "path", "interceptedRoute", "subPath", "pathParts", "split", "i", "length", "cur<PERSON><PERSON>", "startsWith", "add", "redirect", "source", "tokens", "every", "token", "staticFilter", "from", "dynamicFilter", "data", "export"], "mappings": "AACA,SAASA,WAAW,QAAQ,6BAA4B;AACxD,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,mBAAmB,QAAQ,mDAAkD;AAEtF,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SACEC,mCAAmC,EACnCC,0BAA0B,QACrB,iDAAgD;AAEvD,OAAO,SAASC,yBACdC,KAAe,EACfC,SAAqB,EACrBC,gBAAyB;IAKzB,MAAMC,cAAc,IAAIC;IACxB,MAAMC,eAAe,IAAID;IAEzB,KAAK,IAAIE,QAAQN,MAAO;QACtB,IAAIN,eAAeY,OAAO;YACxB,IAAIR,2BAA2BQ,OAAO;gBACpCA,OAAOT,oCAAoCS,MAAMC,gBAAgB;YACnE;YAEA,IAAIC,UAAU;YACd,MAAMC,YAAYH,KAAKI,KAAK,CAAC;YAE7B,uDAAuD;YACvD,kDAAkD;YAClD,IAAK,IAAIC,IAAI,GAAGA,IAAIF,UAAUG,MAAM,EAAED,IAAK;gBACzC,MAAME,UAAUJ,SAAS,CAACE,EAAE;gBAE5B,IAAIE,QAAQC,UAAU,CAAC,MAAM;oBAC3B;gBACF;gBACAN,UAAU,GAAGA,QAAQ,CAAC,EAAEK,SAAS;YACnC;YAEA,IAAIL,SAAS;gBACXH,aAAaU,GAAG,CAACP;YACnB;QACF,OAAO;YACLL,YAAYY,GAAG,CAACT;QAClB;IACF;IAEA,KAAK,MAAMU,YAAYf,UAAW;QAChC,MAAM,EAAEgB,MAAM,EAAE,GAAGD;QACnB,MAAMV,OAAOX,oBAAoBsB;QACjC,IAAIC,SAAkB,EAAE;QAExB,IAAI;YACFA,SAAStB,eAAeqB,QAAQC,MAAM,IAAI,EAAE;QAC9C,EAAE,OAAM,CAAC;QAET,IAAIA,OAAOC,KAAK,CAAC,CAACC,QAAU,OAAOA,UAAU,WAAW;YACtD,0CAA0C;YAC1CjB,YAAYY,GAAG,CAACT;QAClB;IACF;IAEA,MAAMe,eAAe5B,YAAY6B,IAAI,CAAC;WAAInB;KAAY,EAAED;IAExD,MAAMqB,gBAAgB9B,YAAY6B,IAAI,CAAC;WAAIjB;KAAa,EAAEH;IAC1D,MAAMsB,OAAO;QACXH,cAAcA,aAAaI,MAAM;QACjCF,eAAeA,cAAcE,MAAM;IACrC;IACA,OAAOD;AACT"}