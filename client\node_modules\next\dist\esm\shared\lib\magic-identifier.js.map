{"version": 3, "sources": ["../../../src/shared/lib/magic-identifier.ts"], "sourcesContent": ["function decodeHex(hexStr: string): string {\n  if (hexStr.trim() === '') {\n    throw new Error(\"can't decode empty hex\")\n  }\n\n  const num = parseInt(hexStr, 16)\n  if (isNaN(num)) {\n    throw new Error(`invalid hex: \\`${hexStr}\\``)\n  }\n\n  return String.fromCodePoint(num)\n}\n\nconst enum Mode {\n  Text,\n  Underscore,\n  Hex,\n  LongHex,\n}\n\nconst DECODE_REGEX = /^__TURBOPACK__([a-zA-Z0-9_$]+)__$/\n\nexport function decodeMagicIdentifier(identifier: string): string {\n  const matches = identifier.match(DECODE_REGEX)\n  if (!matches) {\n    return identifier\n  }\n\n  const inner = matches[1]\n\n  let output = ''\n\n  let mode: Mode = Mode.Text\n  let buffer = ''\n  for (let i = 0; i < inner.length; i++) {\n    const char = inner[i]\n\n    if (mode === Mode.Text) {\n      if (char === '_') {\n        mode = Mode.Underscore\n      } else if (char === '$') {\n        mode = Mode.Hex\n      } else {\n        output += char\n      }\n    } else if (mode === Mode.Underscore) {\n      if (char === '_') {\n        output += ' '\n        mode = Mode.Text\n      } else if (char === '$') {\n        output += '_'\n        mode = Mode.Hex\n      } else {\n        output += char\n        mode = Mode.Text\n      }\n    } else if (mode === Mode.Hex) {\n      if (buffer.length === 2) {\n        output += decodeHex(buffer)\n        buffer = ''\n      }\n\n      if (char === '_') {\n        if (buffer !== '') {\n          throw new Error(`invalid hex: \\`${buffer}\\``)\n        }\n\n        mode = Mode.LongHex\n      } else if (char === '$') {\n        if (buffer !== '') {\n          throw new Error(`invalid hex: \\`${buffer}\\``)\n        }\n\n        mode = Mode.Text\n      } else {\n        buffer += char\n      }\n    } else if (mode === Mode.LongHex) {\n      if (char === '_') {\n        throw new Error(`invalid hex: \\`${buffer + char}\\``)\n      } else if (char === '$') {\n        output += decodeHex(buffer)\n        buffer = ''\n\n        mode = Mode.Text\n      } else {\n        buffer += char\n      }\n    }\n  }\n\n  return output\n}\n\nexport const MAGIC_IDENTIFIER_REGEX = /__TURBOPACK__[a-zA-Z0-9_$]+__/g\n"], "names": ["decodeHex", "hexStr", "trim", "Error", "num", "parseInt", "isNaN", "String", "fromCodePoint", "DECODE_REGEX", "decodeMagicIdentifier", "identifier", "matches", "match", "inner", "output", "mode", "buffer", "i", "length", "char", "MAGIC_IDENTIFIER_REGEX"], "mappings": "AAAA,SAASA,UAAUC,MAAc;IAC/B,IAAIA,OAAOC,IAAI,OAAO,IAAI;QACxB,MAAM,qBAAmC,CAAnC,IAAIC,MAAM,2BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkC;IAC1C;IAEA,MAAMC,MAAMC,SAASJ,QAAQ;IAC7B,IAAIK,MAAMF,MAAM;QACd,MAAM,qBAAuC,CAAvC,IAAID,MAAM,AAAC,mBAAiBF,SAAO,MAAnC,qBAAA;mBAAA;wBAAA;0BAAA;QAAsC;IAC9C;IAEA,OAAOM,OAAOC,aAAa,CAACJ;AAC9B;;AASA,MAAMK,eAAe;AAErB,OAAO,SAASC,sBAAsBC,UAAkB;IACtD,MAAMC,UAAUD,WAAWE,KAAK,CAACJ;IACjC,IAAI,CAACG,SAAS;QACZ,OAAOD;IACT;IAEA,MAAMG,QAAQF,OAAO,CAAC,EAAE;IAExB,IAAIG,SAAS;IAEb,IAAIC;IACJ,IAAIC,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,MAAMK,MAAM,EAAED,IAAK;QACrC,MAAME,OAAON,KAAK,CAACI,EAAE;QAErB,IAAIF,YAAoB;YACtB,IAAII,SAAS,KAAK;gBAChBJ;YACF,OAAO,IAAII,SAAS,KAAK;gBACvBJ;YACF,OAAO;gBACLD,UAAUK;YACZ;QACF,OAAO,IAAIJ,YAA0B;YACnC,IAAII,SAAS,KAAK;gBAChBL,UAAU;gBACVC;YACF,OAAO,IAAII,SAAS,KAAK;gBACvBL,UAAU;gBACVC;YACF,OAAO;gBACLD,UAAUK;gBACVJ;YACF;QACF,OAAO,IAAIA,YAAmB;YAC5B,IAAIC,OAAOE,MAAM,KAAK,GAAG;gBACvBJ,UAAUf,UAAUiB;gBACpBA,SAAS;YACX;YAEA,IAAIG,SAAS,KAAK;gBAChB,IAAIH,WAAW,IAAI;oBACjB,MAAM,qBAAuC,CAAvC,IAAId,MAAM,AAAC,mBAAiBc,SAAO,MAAnC,qBAAA;+BAAA;oCAAA;sCAAA;oBAAsC;gBAC9C;gBAEAD;YACF,OAAO,IAAII,SAAS,KAAK;gBACvB,IAAIH,WAAW,IAAI;oBACjB,MAAM,qBAAuC,CAAvC,IAAId,MAAM,AAAC,mBAAiBc,SAAO,MAAnC,qBAAA;+BAAA;oCAAA;sCAAA;oBAAsC;gBAC9C;gBAEAD;YACF,OAAO;gBACLC,UAAUG;YACZ;QACF,OAAO,IAAIJ,YAAuB;YAChC,IAAII,SAAS,KAAK;gBAChB,MAAM,qBAA8C,CAA9C,IAAIjB,MAAM,AAAC,mBAAiBc,CAAAA,SAASG,IAAG,IAAE,MAA1C,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6C;YACrD,OAAO,IAAIA,SAAS,KAAK;gBACvBL,UAAUf,UAAUiB;gBACpBA,SAAS;gBAETD;YACF,OAAO;gBACLC,UAAUG;YACZ;QACF;IACF;IAEA,OAAOL;AACT;AAEA,OAAO,MAAMM,yBAAyB,iCAAgC"}