{"version": 3, "sources": ["../../../src/shared/lib/side-effect.tsx"], "sourcesContent": ["import type React from 'react'\nimport { Children, useEffect, useLayoutEffect, type JSX } from 'react'\n\ntype State = JSX.Element[] | undefined\n\nexport type SideEffectProps = {\n  reduceComponentsToState: <T extends {}>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n  children: React.ReactNode\n}\n\nconst isServer = typeof window === 'undefined'\nconst useClientOnlyLayoutEffect = isServer ? () => {} : useLayoutEffect\nconst useClientOnlyEffect = isServer ? () => {} : useEffect\n\nexport default function SideEffect(props: SideEffectProps) {\n  const { headManager, reduceComponentsToState } = props\n\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = Children.toArray(\n        Array.from(headManager.mountedInstances as Set<React.ReactNode>).filter(\n          Boolean\n        )\n      ) as React.ReactElement[]\n      headManager.updateHead(reduceComponentsToState(headElements, props))\n    }\n  }\n\n  if (isServer) {\n    headManager?.mountedInstances?.add(props.children)\n    emitChange()\n  }\n\n  useClientOnlyLayoutEffect(() => {\n    headManager?.mountedInstances?.add(props.children)\n    return () => {\n      headManager?.mountedInstances?.delete(props.children)\n    }\n  })\n\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange\n      }\n    }\n  })\n\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate()\n      headManager._pendingUpdate = null\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate()\n        headManager._pendingUpdate = null\n      }\n    }\n  })\n\n  return null\n}\n"], "names": ["Children", "useEffect", "useLayoutEffect", "isServer", "window", "useClientOnlyLayoutEffect", "useClientOnlyEffect", "SideEffect", "props", "headManager", "reduceComponentsToState", "emitChange", "mountedInstances", "headElements", "toArray", "Array", "from", "filter", "Boolean", "updateHead", "add", "children", "delete", "_pendingUpdate"], "mappings": "AACA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,eAAe,QAAkB,QAAO;AAetE,MAAMC,WAAW,OAAOC,WAAW;AACnC,MAAMC,4BAA4BF,WAAW,KAAO,IAAID;AACxD,MAAMI,sBAAsBH,WAAW,KAAO,IAAIF;AAElD,eAAe,SAASM,WAAWC,KAAsB;IACvD,MAAM,EAAEC,WAAW,EAAEC,uBAAuB,EAAE,GAAGF;IAEjD,SAASG;QACP,IAAIF,eAAeA,YAAYG,gBAAgB,EAAE;YAC/C,MAAMC,eAAeb,SAASc,OAAO,CACnCC,MAAMC,IAAI,CAACP,YAAYG,gBAAgB,EAA0BK,MAAM,CACrEC;YAGJT,YAAYU,UAAU,CAACT,wBAAwBG,cAAcL;QAC/D;IACF;IAEA,IAAIL,UAAU;YACZM;QAAAA,gCAAAA,gCAAAA,YAAaG,gBAAgB,qBAA7BH,8BAA+BW,GAAG,CAACZ,MAAMa,QAAQ;QACjDV;IACF;IAEAN,0BAA0B;YACxBI;QAAAA,gCAAAA,gCAAAA,YAAaG,gBAAgB,qBAA7BH,8BAA+BW,GAAG,CAACZ,MAAMa,QAAQ;QACjD,OAAO;gBACLZ;YAAAA,gCAAAA,gCAAAA,YAAaG,gBAAgB,qBAA7BH,8BAA+Ba,MAAM,CAACd,MAAMa,QAAQ;QACtD;IACF;IAEA,kFAAkF;IAClF,oFAAoF;IACpF,gEAAgE;IAChE,qFAAqF;IACrF,mFAAmF;IACnFhB,0BAA0B;QACxB,IAAII,aAAa;YACfA,YAAYc,cAAc,GAAGZ;QAC/B;QACA,OAAO;YACL,IAAIF,aAAa;gBACfA,YAAYc,cAAc,GAAGZ;YAC/B;QACF;IACF;IAEAL,oBAAoB;QAClB,IAAIG,eAAeA,YAAYc,cAAc,EAAE;YAC7Cd,YAAYc,cAAc;YAC1Bd,YAAYc,cAAc,GAAG;QAC/B;QACA,OAAO;YACL,IAAId,eAAeA,YAAYc,cAAc,EAAE;gBAC7Cd,YAAYc,cAAc;gBAC1Bd,YAAYc,cAAc,GAAG;YAC/B;QACF;IACF;IAEA,OAAO;AACT"}