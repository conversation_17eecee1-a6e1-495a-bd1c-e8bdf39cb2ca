import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import { getAdminUser, saveAdminUser } from '../../data/settings';
import { AdminUser } from '../../types/admin';

const AccountAdmin = () => {
  const [user, setUser] = useState<AdminUser>({
    id: '',
    username: '',
    password: '',
    email: '',
    role: 'admin',
    lastLogin: new Date()
  });
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [activeTab, setActiveTab] = useState('profile');
  const [isSaving, setSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    const currentUser = getAdminUser();
    setUser(currentUser);
    setFormData({
      username: currentUser.username,
      email: currentUser.email,
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
  }, []);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (activeTab === 'profile') {
      if (!formData.username.trim()) {
        newErrors.username = 'اسم المستخدم مطلوب';
      }
      if (!formData.email.trim()) {
        newErrors.email = 'البريد الإلكتروني مطلوب';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'البريد الإلكتروني غير صحيح';
      }
    }

    if (activeTab === 'password') {
      if (!formData.currentPassword) {
        newErrors.currentPassword = 'كلمة المرور الحالية مطلوبة';
      } else if (formData.currentPassword !== user.password) {
        newErrors.currentPassword = 'كلمة المرور الحالية غير صحيحة';
      }
      
      if (!formData.newPassword) {
        newErrors.newPassword = 'كلمة المرور الجديدة مطلوبة';
      } else if (formData.newPassword.length < 6) {
        newErrors.newPassword = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
      }
      
      if (formData.newPassword !== formData.confirmPassword) {
        newErrors.confirmPassword = 'تأكيد كلمة المرور غير متطابق';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setSaving(true);
    setSaveMessage('');

    try {
      // محاكاة تأخير الحفظ
      await new Promise(resolve => setTimeout(resolve, 1000));

      const updatedUser: AdminUser = {
        ...user,
        username: formData.username,
        email: formData.email,
        password: activeTab === 'password' ? formData.newPassword : user.password,
        lastLogin: new Date()
      };

      saveAdminUser(updatedUser);
      setUser(updatedUser);

      if (activeTab === 'password') {
        setFormData({
          ...formData,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      }

      setSaveMessage('تم حفظ التغييرات بنجاح');
      setTimeout(() => setSaveMessage(''), 3000);
    } catch {
      setSaveMessage('حدث خطأ أثناء حفظ التغييرات');
    } finally {
      setSaving(false);
    }
  };

  const tabs = [
    { id: 'profile', title: 'الملف الشخصي', icon: 'ri-user-line' },
    { id: 'password', title: 'كلمة المرور', icon: 'ri-lock-line' },
    { id: 'activity', title: 'النشاط', icon: 'ri-history-line' }
  ];

  return (
    <>
      <Head>
        <title>إعدادات الحساب - لوحة التحكم</title>
        <meta name="description" content="إعدادات حساب المدير" />
      </Head>

      <AdminLayout title="إعدادات الحساب">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">إعدادات الحساب</h1>
              <p className="text-gray-600">إدارة معلومات حسابك الشخصي</p>
            </div>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 disabled:opacity-50 text-white px-6 py-3 rounded-xl flex items-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <i className="ri-save-line text-lg ml-2"></i>
                  حفظ التغييرات
                </>
              )}
            </button>
          </div>

          {/* Save Message */}
          {saveMessage && (
            <div className={`p-4 rounded-lg flex items-center ${
              saveMessage.includes('نجاح') 
                ? 'bg-green-50 border border-green-200 text-green-700' 
                : 'bg-red-50 border border-red-200 text-red-700'
            }`}>
              <i className={`${saveMessage.includes('نجاح') ? 'ri-check-line' : 'ri-error-warning-line'} text-lg ml-2`}></i>
              {saveMessage}
            </div>
          )}

          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
            {/* Tabs */}
            <div className="border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
              <nav className="flex space-x-8 space-x-reverse px-6">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-3 border-b-2 font-medium text-sm transition-all duration-300 transform hover:scale-105 ${
                      activeTab === tab.id
                        ? 'border-primary text-primary bg-primary/5 rounded-t-lg'
                        : 'border-transparent text-gray-500 hover:text-primary hover:border-primary/30 hover:bg-primary/5 rounded-t-lg'
                    }`}
                  >
                    <div className="flex items-center">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center ml-2 ${
                        activeTab === tab.id ? 'bg-primary/20 text-primary' : 'bg-gray-100 text-gray-500'
                      }`}>
                        <i className={`${tab.icon} text-lg`}></i>
                      </div>
                      {tab.title}
                    </div>
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {/* Profile Settings */}
              {activeTab === 'profile' && (
                <div className="space-y-6">
                  <div className="flex items-center space-x-4 space-x-reverse bg-gradient-to-r from-primary/10 to-secondary/10 p-6 rounded-2xl border border-primary/20">
                    <div className="w-24 h-24 bg-gradient-to-r from-primary to-secondary rounded-2xl flex items-center justify-center shadow-lg transform rotate-3 hover:rotate-0 transition-transform duration-300">
                      <i className="ri-user-line text-4xl text-white"></i>
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-800 mb-1">{user.username}</h3>
                      <p className="text-gray-600 mb-2 flex items-center">
                        <i className="ri-mail-line text-lg ml-2 text-primary"></i>
                        {user.email}
                      </p>
                      <span className="inline-block px-4 py-2 text-sm font-bold bg-gradient-to-r from-primary to-secondary text-white rounded-xl shadow-lg">
                        {user.role === 'admin' ? 'مدير النظام' : 'مشرف'}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اسم المستخدم *
                      </label>
                      <input
                        type="text"
                        value={formData.username}
                        onChange={(e) => setFormData({...formData, username: e.target.value})}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.username ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="أدخل اسم المستخدم"
                      />
                      {errors.username && (
                        <p className="text-red-600 text-sm mt-1">{errors.username}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        البريد الإلكتروني *
                      </label>
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.email ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="أدخل البريد الإلكتروني"
                      />
                      {errors.email && (
                        <p className="text-red-600 text-sm mt-1">{errors.email}</p>
                      )}
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <i className="ri-information-line text-blue-600 text-lg ml-2 mt-0.5"></i>
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-1">معلومات مهمة:</p>
                        <ul className="list-disc list-inside space-y-1 text-blue-700">
                          <li>تأكد من استخدام بريد إلكتروني صحيح للتواصل</li>
                          <li>اسم المستخدم يجب أن يكون فريداً</li>
                          <li>سيتم حفظ التغييرات فوراً عند الضغط على زر الحفظ</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Password Settings */}
              {activeTab === 'password' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-800">تغيير كلمة المرور</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        كلمة المرور الحالية *
                      </label>
                      <input
                        type="password"
                        value={formData.currentPassword}
                        onChange={(e) => setFormData({...formData, currentPassword: e.target.value})}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.currentPassword ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="أدخل كلمة المرور الحالية"
                      />
                      {errors.currentPassword && (
                        <p className="text-red-600 text-sm mt-1">{errors.currentPassword}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        كلمة المرور الجديدة *
                      </label>
                      <input
                        type="password"
                        value={formData.newPassword}
                        onChange={(e) => setFormData({...formData, newPassword: e.target.value})}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.newPassword ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="أدخل كلمة المرور الجديدة"
                      />
                      {errors.newPassword && (
                        <p className="text-red-600 text-sm mt-1">{errors.newPassword}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        تأكيد كلمة المرور الجديدة *
                      </label>
                      <input
                        type="password"
                        value={formData.confirmPassword}
                        onChange={(e) => setFormData({...formData, confirmPassword: e.target.value})}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                          errors.confirmPassword ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="أعد إدخال كلمة المرور الجديدة"
                      />
                      {errors.confirmPassword && (
                        <p className="text-red-600 text-sm mt-1">{errors.confirmPassword}</p>
                      )}
                    </div>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <i className="ri-shield-check-line text-yellow-600 text-lg ml-2 mt-0.5"></i>
                      <div className="text-sm text-yellow-800">
                        <p className="font-medium mb-1">نصائح لكلمة مرور قوية:</p>
                        <ul className="list-disc list-inside space-y-1 text-yellow-700">
                          <li>استخدم على الأقل 8 أحرف</li>
                          <li>امزج بين الأحرف الكبيرة والصغيرة</li>
                          <li>أضف أرقام ورموز خاصة</li>
                          <li>تجنب استخدام معلومات شخصية</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Activity Log */}
              {activeTab === 'activity' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-800">سجل النشاط</h3>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                          <i className="ri-login-box-line text-green-600"></i>
                        </div>
                        <div>
                          <p className="font-medium text-gray-800">آخر تسجيل دخول</p>
                          <p className="text-sm text-gray-600">
                            {user.lastLogin ? new Date(user.lastLogin).toLocaleString('ar-SA') : 'غير محدد'}
                          </p>
                        </div>
                      </div>
                      <span className="text-xs text-gray-500">الآن</span>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <i className="ri-user-settings-line text-blue-600"></i>
                        </div>
                        <div>
                          <p className="font-medium text-gray-800">إنشاء الحساب</p>
                          <p className="text-sm text-gray-600">تم إنشاء حساب المدير</p>
                        </div>
                      </div>
                      <span className="text-xs text-gray-500">منذ فترة</span>
                    </div>
                  </div>

                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <i className="ri-information-line text-gray-600 text-lg ml-2 mt-0.5"></i>
                      <div className="text-sm text-gray-700">
                        <p className="font-medium mb-1">حول سجل النشاط:</p>
                        <p>يتم تسجيل جميع الأنشطة المهمة في حسابك هنا، بما في ذلك تسجيل الدخول والتغييرات المهمة.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </AdminLayout>
    </>
  );
};

export default AccountAdmin;
