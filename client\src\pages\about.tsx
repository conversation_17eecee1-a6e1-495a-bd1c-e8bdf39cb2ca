import React from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import WhatsAppButton from '../components/WhatsAppButton';
import Link from 'next/link';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { GetStaticProps } from 'next';

const About = () => {
  const router = useRouter();
  const locale = (router.locale || 'ar') as 'ar' | 'en';
  return (
    <>
      <Head>
        <title>من نحن - DROOB HAJER</title>
        <meta name="description" content="تعرف على شركة دروب هاجر المتخصصة في توفير أفضل تجهيزات الفنادق والمطاعم بمعايير عالمية وجودة عالية" />
        <meta name="keywords" content="من نحن, دروب هاجر, تجهيزات فنادق, تجهيزات مطاعم, شركة سعودية" />
      </Head>
      
      <div className="bg-gray-50 min-h-screen">
        <Navbar locale={locale} />
        
        <main>
          {/* Hero Section */}
          <section className="relative bg-gradient-to-r from-primary to-secondary text-white py-20">
            <div className="absolute inset-0 bg-black/20"></div>
            <div className="container mx-auto px-4 relative z-10">
              <div className="text-center max-w-4xl mx-auto">
                <h1 className="text-4xl md:text-6xl font-bold mb-6">من نحن</h1>
                <p className="text-xl md:text-2xl text-gray-200 leading-relaxed">
                  شركة رائدة في مجال توفير تجهيزات الفنادق والمطاعم بأعلى معايير الجودة والتميز
                </p>
              </div>
            </div>
          </section>

          {/* Company Story Section */}
          <section className="py-20 bg-white">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h2 className="text-3xl md:text-4xl font-bold text-primary mb-6">قصتنا</h2>
                  <p className="text-gray-600 text-lg leading-relaxed mb-6">
                    تأسست شركة دروب هاجر بهدف تقديم حلول متكاملة لتجهيز الفنادق والمطاعم في المملكة العربية السعودية والمنطقة. 
                    منذ انطلاقتنا، نسعى لأن نكون الشريك الأول والموثوق لعملائنا في رحلة النجاح.
                  </p>
                  <p className="text-gray-600 text-lg leading-relaxed mb-6">
                    نؤمن بأن التفاصيل تصنع الفارق، ولذلك نحرص على انتقاء أفضل المنتجات من موردين عالميين موثوقين، 
                    ونقدمها بأسعار تنافسية وخدمة عملاء متميزة.
                  </p>
                  <div className="flex gap-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary">10+</div>
                      <div className="text-gray-600">سنوات خبرة</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary">500+</div>
                      <div className="text-gray-600">عميل راضي</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-primary">1000+</div>
                      <div className="text-gray-600">منتج متنوع</div>
                    </div>
                  </div>
                </div>
                <div className="relative">
                  <div className="bg-gradient-to-br from-primary/10 to-secondary/10 rounded-2xl p-8 h-96 flex items-center justify-center">
                    <i className="ri-building-line text-8xl text-primary/30"></i>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Vision & Mission Section */}
          <section className="py-20 bg-gray-50">
            <div className="container mx-auto px-4">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">رؤيتنا ورسالتنا</h2>
                <p className="text-gray-600 text-lg max-w-2xl mx-auto">
                  نسعى لتحقيق التميز في كل ما نقدمه من خدمات ومنتجات
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-6">
                    <i className="ri-eye-line text-2xl text-primary"></i>
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-4">رؤيتنا</h3>
                  <p className="text-gray-600 leading-relaxed">
                    أن نكون الشركة الرائدة في مجال تجهيزات الفنادق والمطاعم في المنطقة، 
                    ونساهم في رفع مستوى الضيافة والخدمة في القطاع السياحي والفندقي.
                  </p>
                </div>
                
                <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <div className="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mb-6">
                    <i className="ri-target-line text-2xl text-secondary"></i>
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-4">رسالتنا</h3>
                  <p className="text-gray-600 leading-relaxed">
                    تقديم حلول متكاملة ومبتكرة لتجهيز الفنادق والمطاعم بأعلى معايير الجودة، 
                    مع ضمان رضا العملاء وتحقيق شراكات طويلة الأمد.
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Values Section */}
          <section className="py-20 bg-white">
            <div className="container mx-auto px-4">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">قيمنا</h2>
                <p className="text-gray-600 text-lg max-w-2xl mx-auto">
                  القيم التي نؤمن بها وتوجه عملنا اليومي
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div className="text-center group">
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary group-hover:text-white transition-all duration-300">
                    <i className="ri-award-line text-3xl text-primary group-hover:text-white"></i>
                  </div>
                  <h3 className="text-xl font-bold text-primary mb-3">الجودة</h3>
                  <p className="text-gray-600">
                    نلتزم بتقديم منتجات عالية الجودة تلبي أعلى المعايير العالمية
                  </p>
                </div>
                
                <div className="text-center group">
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary group-hover:text-white transition-all duration-300">
                    <i className="ri-customer-service-line text-3xl text-primary group-hover:text-white"></i>
                  </div>
                  <h3 className="text-xl font-bold text-primary mb-3">خدمة العملاء</h3>
                  <p className="text-gray-600">
                    نضع رضا عملائنا في المقدمة ونسعى لتجاوز توقعاتهم
                  </p>
                </div>
                
                <div className="text-center group">
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary group-hover:text-white transition-all duration-300">
                    <i className="ri-lightbulb-line text-3xl text-primary group-hover:text-white"></i>
                  </div>
                  <h3 className="text-xl font-bold text-primary mb-3">الابتكار</h3>
                  <p className="text-gray-600">
                    نبحث باستمرار عن حلول مبتكرة لتطوير خدماتنا ومنتجاتنا
                  </p>
                </div>
                
                <div className="text-center group">
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-primary group-hover:text-white transition-all duration-300">
                    <i className="ri-handshake-line text-3xl text-primary group-hover:text-white"></i>
                  </div>
                  <h3 className="text-xl font-bold text-primary mb-3">الشراكة</h3>
                  <p className="text-gray-600">
                    نؤمن ببناء شراكات طويلة الأمد مع عملائنا وموردينا
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Team Section */}
          <section className="py-20 bg-gray-50">
            <div className="container mx-auto px-4">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">فريق العمل</h2>
                <p className="text-gray-600 text-lg max-w-2xl mx-auto">
                  فريق من الخبراء المتخصصين في مجال تجهيزات الفنادق والمطاعم
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div className="bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <div className="w-24 h-24 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center mx-auto mb-6">
                    <i className="ri-user-line text-3xl text-white"></i>
                  </div>
                  <h3 className="text-xl font-bold text-primary mb-2">أحمد محمد</h3>
                  <p className="text-secondary mb-4">المدير التنفيذي</p>
                  <p className="text-gray-600 text-sm">
                    خبرة تزيد عن 15 عاماً في مجال إدارة الأعمال وتطوير الشركات
                  </p>
                </div>
                
                <div className="bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <div className="w-24 h-24 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center mx-auto mb-6">
                    <i className="ri-user-line text-3xl text-white"></i>
                  </div>
                  <h3 className="text-xl font-bold text-primary mb-2">سارة أحمد</h3>
                  <p className="text-secondary mb-4">مديرة المبيعات</p>
                  <p className="text-gray-600 text-sm">
                    متخصصة في بناء العلاقات مع العملاء وتطوير استراتيجيات المبيعات
                  </p>
                </div>
                
                <div className="bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <div className="w-24 h-24 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center mx-auto mb-6">
                    <i className="ri-user-line text-3xl text-white"></i>
                  </div>
                  <h3 className="text-xl font-bold text-primary mb-2">محمد علي</h3>
                  <p className="text-secondary mb-4">مدير العمليات</p>
                  <p className="text-gray-600 text-sm">
                    خبير في إدارة سلسلة التوريد وضمان جودة المنتجات والخدمات
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="py-20 bg-gradient-to-r from-primary to-secondary text-white">
            <div className="container mx-auto px-4 text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">هل أنت مستعد للبدء؟</h2>
              <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
                انضم إلى مئات العملاء الذين يثقون بنا في تجهيز مشاريعهم الفندقية والمطاعم
              </p>
                <Link href="/contact" passHref legacyBehavior>
                  <a className="bg-white text-primary px-8 py-4 rounded-lg font-bold hover:bg-gray-100 transition-colors duration-300">
                    تواصل معنا
                  </a>
                </Link>
                <Link href="/products" passHref legacyBehavior>
                  <a className="border-2 border-white text-white px-8 py-4 rounded-lg font-bold hover:bg-white hover:text-primary transition-colors duration-300">
                    تصفح المنتجات
                  </a>
                </Link>
              </div>
           </section>
        </main>
        
        <Footer locale={locale} />
        <WhatsAppButton />
      </div>
    </>
  );
};

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ar', ['common'])),
    },
  };
};

export default About;
