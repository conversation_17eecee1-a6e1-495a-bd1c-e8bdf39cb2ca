{"name": "vidmeet-monorepo", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "start": "concurrently \"npm run start:client\" \"npm run start:server\"", "start:client": "cd client && npm run start", "start:server": "cd server && npm run start", "install:all": "npm install && cd client && npm install && cd ../server && npm install", "clean": "rimraf client/.next server/dist"}, "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^6.0.1"}}