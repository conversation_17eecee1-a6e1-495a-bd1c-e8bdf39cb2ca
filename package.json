{"name": "vid<PERSON>t", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "clean": "rimraf .next", "build": "rimraf .next && next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/formidable": "^3.4.5", "@types/nodemailer": "^6.4.17", "clsx": "^2.1.0", "formidable": "^3.5.4", "framer-motion": "^12.9.2", "fs-extra": "^11.2.0", "next": "15.3.1", "next-i18next": "^15.4.2", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "rtl-detect": "^1.1.2", "simple-peer": "^9.11.1", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.2.1", "uuid": "^9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/fs-extra": "^11.0.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/simple-peer": "^9.11.8", "@types/uuid": "^9.0.8", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8.5.3", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5"}}