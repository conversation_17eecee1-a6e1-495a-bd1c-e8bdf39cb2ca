export interface Category {
  id: string;
  name: string;
  nameAr: string;
  description: string;
  descriptionAr: string;
  image: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Subcategory {
  id: string;
  name: string;
  nameAr: string;
  categoryId: string;
  description: string;
  descriptionAr: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SpecificationItem {
  nameEn: string;
  nameAr: string;
  valueEn: string;
  valueAr: string;
}

export interface Product {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  images: string[];
  price: number;
  originalPrice: number | null;
  available: boolean;
  categoryId: string;
  subcategoryId: string;
  features: string[];
  featuresAr: string[];
  specifications: SpecificationItem[];
  isActive: boolean;
  isFeatured: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SiteSettings {
  siteName: string;
  siteNameAr: string;
  contactEmail: string;
  whatsappNumber: string;
  socialLinks: {
    facebook: string;
    instagram: string;
    twitter: string;
    linkedin: string;
    youtube: string;
  };
  heroImages: string[];
  aboutText: string;
  aboutTextAr: string;
  address: string;
  addressAr: string;
  phone: string;
  workingHours: string;
  workingHoursAr: string;
  contactSettings: {
    mapSettings: {
      latitude: number;
      longitude: number;
      zoom: number;
      googleMapsUrl?: string;
      showMap: boolean;
    };
    contactFormSettings: {
      enableContactForm: boolean;
      successMessage: string;
      successMessageAr: string;
      errorMessage: string;
      errorMessageAr: string;
    };
    officeHours: {
      enabled: boolean;
      hoursText: string;
      hoursTextAr: string;
    };
    additionalInfo: {
      description: string;
      descriptionAr: string;
      showFAQ: boolean;
    };
  };
  aboutSettings: {
    heroSection: {
      title: string;
      titleAr: string;
      subtitle: string;
      subtitleAr: string;
    };
    description: {
      text: string;
      textAr: string;
    };
    vision: {
      title: string;
      titleAr: string;
      text: string;
      textAr: string;
    };
    mission: {
      title: string;
      titleAr: string;
      text: string;
      textAr: string;
    };
    values: {
      title: string;
      titleAr: string;
      items: Array<{
        titleEn: string;
        titleAr: string;
        descriptionEn: string;
        descriptionAr: string;
        icon: string;
      }>;
    };
    team: {
      title: string;
      titleAr: string;
      description: string;
      descriptionAr: string;
    };
    contactCTA: {
      title: string;
      titleAr: string;
      description: string;
      descriptionAr: string;
      enabled: boolean;
    };
  };
  partnersSettings: {
    title: string;
    titleAr: string;
    description: string;
    descriptionAr: string;
    enabled: boolean;
    items: Array<{
      nameEn: string;
      nameAr: string;
      logo: string;
      website?: string;
      description?: string;
      descriptionAr?: string;
    }>;
  };
}

export interface AdminUser {
  id: string;
  username: string;
  password: string;
  email: string;
  role: string;
  lastLogin: string;
}

export interface Database {
  categories: Category[];
  subcategories: Subcategory[];
  products: Product[];
  settings: SiteSettings;
  adminUser: AdminUser;
}
