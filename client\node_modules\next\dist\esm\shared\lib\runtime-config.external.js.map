{"version": 3, "sources": ["../../../src/shared/lib/runtime-config.external.ts"], "sourcesContent": ["let runtimeConfig: any\n\nexport default () => {\n  return runtimeConfig\n}\n\nexport function setConfig(configValue: any): void {\n  runtimeConfig = configValue\n}\n"], "names": ["runtimeConfig", "setConfig", "config<PERSON><PERSON><PERSON>"], "mappings": "AAAA,IAAIA;AAEJ,eAAe,CAAA;IACb,OAAOA;AACT,CAAA,EAAC;AAED,OAAO,SAASC,UAAUC,WAAgB;IACxCF,gBAAgBE;AAClB"}