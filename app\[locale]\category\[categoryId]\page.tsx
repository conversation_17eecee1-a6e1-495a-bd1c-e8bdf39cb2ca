'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Locale } from '../../../../lib/i18n';
import { getTranslation } from '../../../../lib/translations';
import Navbar from '../../../../components/Navbar';
import Footer from '../../../../components/Footer';
import ProductCard from '../../../../components/ProductCard';
import WhatsAppButton from '../../../../components/WhatsAppButton';
import SubcategoryFilter from '../../../../components/SubcategoryFilter';
import { Product, Category, Subcategory } from '../../../../src/types/database';
import { productsApi, categoriesApi, subcategoriesApi } from '../../../../src/lib/api';

export default function CategoryPage() {
  const params = useParams();
  const router = useRouter();
  const locale = (params?.locale || 'ar') as Locale;
  const categoryId = params?.categoryId as string;
  
  const [products, setProducts] = useState<Product[]>([]);
  const [category, setCategory] = useState<Category | null>(null);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubcategory, setSelectedSubcategory] = useState('all');
  const [priceRange, setPriceRange] = useState({ min: 0, max: 10000 });
  const [availabilityFilter, setAvailabilityFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');

  const t = (key: string) => getTranslation(locale, key as any);

  useEffect(() => {
    if (categoryId) {
      fetchCategoryData();
    }
  }, [categoryId]);

  const fetchCategoryData = async () => {
    try {
      setLoading(true);
      
      // جلب بيانات الفئة
      const categoriesData = await categoriesApi.getAll();
      const currentCategory = categoriesData.find(cat => cat.id === categoryId);
      
      if (!currentCategory) {
        router.push(`/${locale}/products`);
        return;
      }
      
      setCategory(currentCategory);
      
      // جلب المنتجات الخاصة بالفئة
      const productsData = await productsApi.getAll();
      const categoryProducts = productsData.filter(product => 
        product.categoryId === categoryId && product.isActive
      );
      setProducts(categoryProducts);
      
      // جلب الفئات الفرعية
      const subcategoriesData = await subcategoriesApi.getAll();
      const categorySubcategories = subcategoriesData.filter(sub => 
        sub.categoryId === categoryId && sub.isActive
      );
      setSubcategories(categorySubcategories);
      
      // تحديد نطاق الأسعار
      if (categoryProducts.length > 0) {
        const prices = categoryProducts.map(p => p.price);
        setPriceRange({
          min: Math.min(...prices),
          max: Math.max(...prices)
        });
      }
      
    } catch (error) {
      console.error('Error fetching category data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredProducts = products.filter(product => {
    // فلتر البحث
    const searchField = locale === 'ar' ? product.titleAr : product.title;
    const matchesSearch = searchField.toLowerCase().includes(searchTerm.toLowerCase());
    
    // فلتر الفئة الفرعية
    const matchesSubcategory = selectedSubcategory === 'all' || 
      product.subcategoryId === selectedSubcategory;
    
    // فلتر السعر
    const matchesPrice = product.price >= priceRange.min && product.price <= priceRange.max;
    
    // فلتر التوفر
    const matchesAvailability = availabilityFilter === 'all' || 
      (availabilityFilter === 'available' && product.available) ||
      (availabilityFilter === 'unavailable' && !product.available);
    
    return matchesSearch && matchesSubcategory && matchesPrice && matchesAvailability;
  });

  // ترتيب المنتجات
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        const nameA = locale === 'ar' ? a.titleAr : a.title;
        const nameB = locale === 'ar' ? b.titleAr : b.title;
        return nameA.localeCompare(nameB);
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'newest':
        return new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime();
      default:
        return 0;
    }
  });

  if (loading) {
    return (
      <>
        <Navbar locale={locale} />
        <main className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-16">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/20 border-t-primary mx-auto mb-6"></div>
              <p className="text-gray-700 text-lg font-medium">
                {locale === 'ar' ? 'جاري تحميل منتجات الفئة...' : 'Loading category products...'}
              </p>
            </div>
          </div>
        </main>
        <Footer locale={locale} />
        <WhatsAppButton locale={locale} />
      </>
    );
  }

  if (!category) {
    return (
      <>
        <Navbar locale={locale} />
        <main className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-16">
              <i className="ri-error-warning-line text-6xl text-red-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                {locale === 'ar' ? 'الفئة غير موجودة' : 'Category not found'}
              </h3>
              <p className="text-gray-600 mb-6">
                {locale === 'ar' 
                  ? 'الفئة التي تبحث عنها غير موجودة أو تم حذفها'
                  : 'The category you are looking for does not exist or has been deleted'
                }
              </p>
              <button
                onClick={() => router.push(`/${locale}/products`)}
                className="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300"
              >
                {locale === 'ar' ? 'العودة للمنتجات' : 'Back to Products'}
              </button>
            </div>
          </div>
        </main>
        <Footer locale={locale} />
        <WhatsAppButton locale={locale} />
      </>
    );
  }

  return (
    <>
      <Navbar locale={locale} />
      <main className="min-h-screen bg-gray-50">
        {/* Page Header */}
        <section className="bg-gradient-to-r from-blue-900 via-blue-800 to-blue-700 py-16">
          <div className="container mx-auto px-4">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-white/80 mb-6">
              <a href={`/${locale}`} className="hover:text-white transition-colors">
                {t('home')}
              </a>
              <span>/</span>
              <a href={`/${locale}/products`} className="hover:text-white transition-colors">
                {t('products')}
              </a>
              <span>/</span>
              <span className="text-white font-medium">
                {locale === 'ar' ? category.nameAr : category.name}
              </span>
            </nav>
            
            <div className="text-center text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {locale === 'ar' ? category.nameAr : category.name}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto mb-6">
                {locale === 'ar' ? category.descriptionAr : category.description}
              </p>
              <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse text-white/80">
                <span className="flex items-center space-x-2 rtl:space-x-reverse">
                  <i className="ri-shopping-bag-line"></i>
                  <span>{filteredProducts.length} {locale === 'ar' ? 'منتج' : 'Products'}</span>
                </span>
                {subcategories.length > 0 && (
                  <span className="flex items-center space-x-2 rtl:space-x-reverse">
                    <i className="ri-folder-line"></i>
                    <span>{subcategories.length} {locale === 'ar' ? 'فئة فرعية' : 'Subcategories'}</span>
                  </span>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* Products Section */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Sidebar Filters */}
              <div className="lg:w-1/4">
                {/* Subcategory Filter */}
                {subcategories.length > 0 && (
                  <SubcategoryFilter
                    locale={locale}
                    subcategories={subcategories}
                    selectedSubcategory={selectedSubcategory}
                    onSubcategoryChange={setSelectedSubcategory}
                    productCounts={{
                      all: products.length,
                      ...subcategories.reduce((acc, sub) => ({
                        ...acc,
                        [sub.id]: products.filter(p => p.subcategoryId === sub.id).length
                      }), {})
                    }}
                  />
                )}
                {/* Search Filter */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {locale === 'ar' ? 'البحث' : 'Search'}
                  </label>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    placeholder={locale === 'ar' ? 'ابحث في المنتجات...' : 'Search products...'}
                  />
                </div>

                {/* Availability Filter */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {locale === 'ar' ? 'التوفر' : 'Availability'}
                  </label>
                  <select
                    value={availabilityFilter}
                    onChange={(e) => setAvailabilityFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  >
                    <option value="all">
                      {locale === 'ar' ? 'جميع المنتجات' : 'All Products'}
                    </option>
                    <option value="available">
                      {locale === 'ar' ? 'متاح فقط' : 'Available Only'}
                    </option>
                    <option value="unavailable">
                      {locale === 'ar' ? 'غير متاح' : 'Unavailable'}
                    </option>
                  </select>
                </div>

                {/* Sort Filter */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {locale === 'ar' ? 'ترتيب حسب' : 'Sort by'}
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  >
                    <option value="name">
                      {locale === 'ar' ? 'الاسم' : 'Name'}
                    </option>
                    <option value="price-low">
                      {locale === 'ar' ? 'السعر: من الأقل للأعلى' : 'Price: Low to High'}
                    </option>
                    <option value="price-high">
                      {locale === 'ar' ? 'السعر: من الأعلى للأقل' : 'Price: High to Low'}
                    </option>
                    <option value="newest">
                      {locale === 'ar' ? 'الأحدث' : 'Newest'}
                    </option>
                  </select>
                </div>

                {/* Price Range Filter */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {locale === 'ar' ? 'نطاق الأسعار' : 'Price Range'}
                  </label>
                  <div className="space-y-3">
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">
                        {locale === 'ar' ? 'الحد الأدنى' : 'Min Price'}
                      </label>
                      <input
                        type="number"
                        value={priceRange.min}
                        onChange={(e) => setPriceRange(prev => ({ ...prev, min: Number(e.target.value) }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                        min="0"
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-600 mb-1">
                        {locale === 'ar' ? 'الحد الأقصى' : 'Max Price'}
                      </label>
                      <input
                        type="number"
                        value={priceRange.max}
                        onChange={(e) => setPriceRange(prev => ({ ...prev, max: Number(e.target.value) }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
                        min="0"
                        placeholder="10000"
                      />
                    </div>
                    <button
                      onClick={() => {
                        if (products.length > 0) {
                          const prices = products.map(p => p.price);
                          setPriceRange({
                            min: Math.min(...prices),
                            max: Math.max(...prices)
                          });
                        }
                      }}
                      className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm transition-colors duration-200"
                    >
                      {locale === 'ar' ? 'إعادة تعيين' : 'Reset'}
                    </button>
                  </div>
                </div>

                {/* Clear All Filters */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedSubcategory('all');
                      setAvailabilityFilter('all');
                      setSortBy('name');
                      if (products.length > 0) {
                        const prices = products.map(p => p.price);
                        setPriceRange({
                          min: Math.min(...prices),
                          max: Math.max(...prices)
                        });
                      }
                    }}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                  >
                    {locale === 'ar' ? 'مسح جميع الفلاتر' : 'Clear All Filters'}
                  </button>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:w-3/4">

                {/* Results Summary */}
                <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100 mb-6">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">
                      {locale === 'ar' ? 'عرض' : 'Showing'}
                      <span className="font-semibold text-blue-600 mx-1">{sortedProducts.length}</span>
                      {locale === 'ar' ? 'من أصل' : 'of'}
                      <span className="font-semibold text-blue-600 mx-1">{products.length}</span>
                      {locale === 'ar' ? 'منتج' : 'products'}
                    </span>
                    <div className="flex items-center space-x-2 rtl:space-x-reverse">
                      <i className="ri-filter-line text-blue-600"></i>
                      <span className="text-sm text-gray-500">
                        {locale === 'ar' ? 'استخدم الفلاتر للتصفية' : 'Use filters to refine'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Products Grid */}
                {sortedProducts.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {sortedProducts.map((product) => (
                      <ProductCard
                        key={product.id}
                        id={parseInt(product.id)}
                        image={product.images[0]}
                        title={locale === 'ar' ? product.titleAr : product.title}
                        description={locale === 'ar' ? product.descriptionAr : product.description}
                        price={product.price}
                        available={product.available}
                        locale={locale}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-16 bg-white rounded-2xl shadow-lg">
                    <i className="ri-search-line text-6xl text-gray-400 mb-4"></i>
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">
                      {locale === 'ar' ? 'لا توجد منتجات' : 'No products found'}
                    </h3>
                    <p className="text-gray-600 mb-6">
                      {locale === 'ar'
                        ? 'جرب تغيير معايير البحث أو الفلترة'
                        : 'Try changing your search or filter criteria'
                      }
                    </p>
                    <button
                      onClick={() => {
                        setSearchTerm('');
                        setSelectedSubcategory('all');
                        setAvailabilityFilter('all');
                        setSortBy('name');
                        if (products.length > 0) {
                          const prices = products.map(p => p.price);
                          setPriceRange({
                            min: Math.min(...prices),
                            max: Math.max(...prices)
                          });
                        }
                      }}
                      className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105"
                    >
                      {locale === 'ar' ? 'مسح جميع الفلاتر' : 'Clear All Filters'}
                    </button>
                  </div>
                )}

                {/* Back to Categories */}
                <div className="mt-12 text-center">
                  <button
                    onClick={() => router.push(`/${locale}/products`)}
                    className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 flex items-center justify-center mx-auto space-x-2 rtl:space-x-reverse"
                  >
                    <i className="ri-arrow-left-line"></i>
                    <span>{locale === 'ar' ? 'العودة لجميع المنتجات' : 'Back to All Products'}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
