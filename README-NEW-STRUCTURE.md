# VidMeet - البنية الجديدة المنظمة

## 📁 هيكل المشروع الجديد

```
project-root/
├── client/                 # Frontend (Next.js)
│   ├── app/               # Next.js App Router
│   ├── components/        # React Components
│   ├── lib/              # Frontend utilities
│   ├── public/           # Static assets
│   ├── src/              # Additional frontend code
│   ├── styles/           # CSS files
│   ├── next.config.js    # Next.js configuration
│   ├── tailwind.config.js # Tailwind configuration
│   └── package.json      # Frontend dependencies
├── server/                # Backend (Express.js)
│   ├── src/              # Main server code
│   ├── api/              # API routes
│   ├── admin/            # Admin dashboard
│   ├── database/         # Database logic
│   ├── uploads/          # File uploads
│   ├── utils/            # Server utilities
│   ├── tsconfig.json     # TypeScript config
│   └── package.json      # Backend dependencies
├── shared/               # Shared code
│   ├── types/           # TypeScript types
│   ├── constants/       # Shared constants
│   └── utils/           # Shared utilities
└── package.json         # Root package.json (scripts)
```

## 🚀 كيفية التشغيل

### تثبيت التبعيات
```bash
# تثبيت جميع التبعيات
npm run install:all

# أو تثبيت كل قسم منفصل
npm install          # Root dependencies
cd client && npm install
cd ../server && npm install
```

### تشغيل المشروع

#### تشغيل كامل (Frontend + Backend)
```bash
npm run dev
```

#### تشغيل منفصل
```bash
# Frontend only
npm run dev:client

# Backend only  
npm run dev:server
```

### البناء والإنتاج
```bash
# بناء كامل
npm run build

# بناء منفصل
npm run build:client
npm run build:server
```

### تشغيل الإنتاج
```bash
npm run start
```

## 🔧 المنافذ

- **Frontend (Client)**: http://localhost:3000
- **Backend (Server)**: http://localhost:5000
- **Admin Dashboard**: http://localhost:5000/admin

## 📋 المزايا الجديدة

### ✅ فصل كامل بين Frontend و Backend
- كل قسم له dependencies منفصلة
- يمكن deploy كل قسم منفصل
- سهولة في التطوير والصيانة

### ✅ تنظيم أفضل للكود
- ملفات مشتركة في shared/
- API منفصل في server/
- Frontend منظم في client/

### ✅ مرونة في التطوير
- يمكن تشغيل Frontend أو Backend منفصل
- سهولة إضافة مطورين جدد
- إمكانية استخدام تقنيات مختلفة لكل قسم

## 🔄 التحديثات المطلوبة

بعد إعادة التنظيم، تحتاج إلى:

1. **تحديث المسارات**: تحديث import statements
2. **تحديث API calls**: تغيير URLs للـ API
3. **اختبار الوظائف**: التأكد من عمل جميع الميزات

## 📞 الدعم

إذا واجهت أي مشاكل، تأكد من:
- تثبيت جميع التبعيات
- تشغيل كل من Frontend و Backend
- التحقق من المنافذ المستخدمة
