{"version": 3, "sources": ["../../../src/export/routes/app-route.ts"], "sourcesContent": ["import type { ExportRouteResult } from '../types'\nimport type AppRouteRouteModule from '../../server/route-modules/app-route/module'\nimport type { AppRouteRouteHandlerContext } from '../../server/route-modules/app-route/module'\nimport type { IncrementalCache } from '../../server/lib/incremental-cache'\n\nimport {\n  INFINITE_CACHE,\n  NEXT_BODY_SUFFIX,\n  NEXT_CACHE_TAGS_HEADER,\n  NEXT_META_SUFFIX,\n} from '../../lib/constants'\nimport { NodeNextRequest } from '../../server/base-http/node'\nimport {\n  NextRequestAdapter,\n  signalFromNodeResponse,\n} from '../../server/web/spec-extension/adapters/next-request'\nimport { toNodeOutgoingHttpHeaders } from '../../server/web/utils'\nimport type {\n  MockedRequest,\n  MockedResponse,\n} from '../../server/lib/mock-request'\nimport { isDynamicUsageError } from '../helpers/is-dynamic-usage-error'\nimport { hasNextSupport } from '../../server/ci-info'\nimport { isStaticGenEnabled } from '../../server/route-modules/app-route/helpers/is-static-gen-enabled'\nimport type { ExperimentalConfig } from '../../server/config-shared'\nimport { isMetadataRoute } from '../../lib/metadata/is-metadata-route'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport type { Params } from '../../server/request/params'\nimport { AfterRunner } from '../../server/after/run-with-after'\nimport type { MultiFileWriter } from '../../lib/multi-file-writer'\n\nexport const enum ExportedAppRouteFiles {\n  BODY = 'BODY',\n  META = 'META',\n}\n\nexport async function exportAppRoute(\n  req: MockedRequest,\n  res: MockedResponse,\n  params: Params | undefined,\n  page: string,\n  module: AppRouteRouteModule,\n  incrementalCache: IncrementalCache | undefined,\n  cacheLifeProfiles:\n    | undefined\n    | {\n        [profile: string]: import('../../server/use-cache/cache-life').CacheLife\n      },\n  htmlFilepath: string,\n  fileWriter: MultiFileWriter,\n  experimental: Required<\n    Pick<ExperimentalConfig, 'dynamicIO' | 'authInterrupts'>\n  >,\n  buildId: string\n): Promise<ExportRouteResult> {\n  // Ensure that the URL is absolute.\n  req.url = `http://localhost:3000${req.url}`\n\n  // Adapt the request and response to the Next.js request and response.\n  const request = NextRequestAdapter.fromNodeNextRequest(\n    new NodeNextRequest(req),\n    signalFromNodeResponse(res)\n  )\n\n  const afterRunner = new AfterRunner()\n\n  // Create the context for the handler. This contains the params from\n  // the route and the context for the request.\n  const context: AppRouteRouteHandlerContext = {\n    params,\n    prerenderManifest: {\n      version: 4,\n      routes: {},\n      dynamicRoutes: {},\n      preview: {\n        previewModeEncryptionKey: '',\n        previewModeId: '',\n        previewModeSigningKey: '',\n      },\n      notFoundRoutes: [],\n    },\n    renderOpts: {\n      experimental,\n      nextExport: true,\n      supportsDynamicResponse: false,\n      incrementalCache,\n      waitUntil: afterRunner.context.waitUntil,\n      onClose: afterRunner.context.onClose,\n      onAfterTaskError: afterRunner.context.onTaskError,\n      cacheLifeProfiles,\n    },\n    sharedContext: {\n      buildId,\n    },\n  }\n\n  if (hasNextSupport) {\n    context.renderOpts.isRevalidate = true\n  }\n\n  try {\n    const userland = module.userland\n    // we don't bail from the static optimization for\n    // metadata routes, since it's app-route we can always append /route suffix.\n    const routePath = normalizeAppPath(page) + '/route'\n    const isPageMetadataRoute = isMetadataRoute(routePath)\n\n    if (\n      !isStaticGenEnabled(userland) &&\n      !isPageMetadataRoute &&\n      // We don't disable static gen when dynamicIO is enabled because we\n      // expect that anything dynamic in the GET handler will make it dynamic\n      // and thus avoid the cache surprises that led to us removing static gen\n      // unless specifically opted into\n      experimental.dynamicIO !== true\n    ) {\n      return { cacheControl: { revalidate: 0, expire: undefined } }\n    }\n\n    const response = await module.handle(request, context)\n\n    const isValidStatus = response.status < 400 || response.status === 404\n    if (!isValidStatus) {\n      return { cacheControl: { revalidate: 0, expire: undefined } }\n    }\n\n    const blob = await response.blob()\n\n    // TODO(after): if we abort a prerender because of an error in an after-callback\n    // we should probably communicate that better (and not log the error twice)\n    await afterRunner.executeAfter()\n\n    const revalidate =\n      typeof context.renderOpts.collectedRevalidate === 'undefined' ||\n      context.renderOpts.collectedRevalidate >= INFINITE_CACHE\n        ? false\n        : context.renderOpts.collectedRevalidate\n\n    const expire =\n      typeof context.renderOpts.collectedExpire === 'undefined' ||\n      context.renderOpts.collectedExpire >= INFINITE_CACHE\n        ? undefined\n        : context.renderOpts.collectedExpire\n\n    const headers = toNodeOutgoingHttpHeaders(response.headers)\n    const cacheTags = context.renderOpts.collectedTags\n\n    if (cacheTags) {\n      headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n    }\n\n    if (!headers['content-type'] && blob.type) {\n      headers['content-type'] = blob.type\n    }\n\n    // Writing response body to a file.\n    const body = Buffer.from(await blob.arrayBuffer())\n    fileWriter.append(htmlFilepath.replace(/\\.html$/, NEXT_BODY_SUFFIX), body)\n\n    // Write the request metadata to a file.\n    const meta = { status: response.status, headers }\n    fileWriter.append(\n      htmlFilepath.replace(/\\.html$/, NEXT_META_SUFFIX),\n      JSON.stringify(meta)\n    )\n\n    return {\n      cacheControl: { revalidate, expire },\n      metadata: meta,\n    }\n  } catch (err) {\n    if (!isDynamicUsageError(err)) {\n      throw err\n    }\n\n    return { cacheControl: { revalidate: 0, expire: undefined } }\n  }\n}\n"], "names": ["ExportedAppRouteFiles", "exportAppRoute", "req", "res", "params", "page", "module", "incrementalCache", "cacheLifeProfiles", "htmlFilepath", "fileWriter", "experimental", "buildId", "url", "request", "NextRequestAdapter", "fromNodeNextRequest", "NodeNextRequest", "signalFromNodeResponse", "after<PERSON><PERSON>ner", "After<PERSON><PERSON>ner", "context", "prerenderManifest", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "renderOpts", "nextExport", "supportsDynamicResponse", "waitUntil", "onClose", "onAfterTaskError", "onTaskError", "sharedContext", "hasNextSupport", "isRevalidate", "userland", "routePath", "normalizeAppPath", "isPageMetadataRoute", "isMetadataRoute", "isStaticGenEnabled", "dynamicIO", "cacheControl", "revalidate", "expire", "undefined", "response", "handle", "isValidStatus", "status", "blob", "executeAfter", "collectedRevalidate", "INFINITE_CACHE", "collectedExpire", "headers", "toNodeOutgoingHttpHeaders", "cacheTags", "collectedTags", "NEXT_CACHE_TAGS_HEADER", "type", "body", "<PERSON><PERSON><PERSON>", "from", "arrayBuffer", "append", "replace", "NEXT_BODY_SUFFIX", "meta", "NEXT_META_SUFFIX", "JSON", "stringify", "metadata", "err", "isDynamicUsageError"], "mappings": ";;;;;;;;;;;;;;;IA+BkBA,qBAAqB;eAArBA;;IAKIC,cAAc;eAAdA;;;2BA1Bf;sBACyB;6BAIzB;uBACmC;qCAKN;wBACL;oCACI;iCAEH;0BACC;8BAEL;AAGrB,IAAA,AAAWD,+CAAAA;;;WAAAA;;AAKX,eAAeC,eACpBC,GAAkB,EAClBC,GAAmB,EACnBC,MAA0B,EAC1BC,IAAY,EACZC,OAA2B,EAC3BC,gBAA8C,EAC9CC,iBAIK,EACLC,YAAoB,EACpBC,UAA2B,EAC3BC,YAEC,EACDC,OAAe;IAEf,mCAAmC;IACnCV,IAAIW,GAAG,GAAG,CAAC,qBAAqB,EAAEX,IAAIW,GAAG,EAAE;IAE3C,sEAAsE;IACtE,MAAMC,UAAUC,+BAAkB,CAACC,mBAAmB,CACpD,IAAIC,qBAAe,CAACf,MACpBgB,IAAAA,mCAAsB,EAACf;IAGzB,MAAMgB,cAAc,IAAIC,yBAAW;IAEnC,oEAAoE;IACpE,6CAA6C;IAC7C,MAAMC,UAAuC;QAC3CjB;QACAkB,mBAAmB;YACjBC,SAAS;YACTC,QAAQ,CAAC;YACTC,eAAe,CAAC;YAChBC,SAAS;gBACPC,0BAA0B;gBAC1BC,eAAe;gBACfC,uBAAuB;YACzB;YACAC,gBAAgB,EAAE;QACpB;QACAC,YAAY;YACVpB;YACAqB,YAAY;YACZC,yBAAyB;YACzB1B;YACA2B,WAAWf,YAAYE,OAAO,CAACa,SAAS;YACxCC,SAAShB,YAAYE,OAAO,CAACc,OAAO;YACpCC,kBAAkBjB,YAAYE,OAAO,CAACgB,WAAW;YACjD7B;QACF;QACA8B,eAAe;YACb1B;QACF;IACF;IAEA,IAAI2B,sBAAc,EAAE;QAClBlB,QAAQU,UAAU,CAACS,YAAY,GAAG;IACpC;IAEA,IAAI;QACF,MAAMC,WAAWnC,QAAOmC,QAAQ;QAChC,iDAAiD;QACjD,4EAA4E;QAC5E,MAAMC,YAAYC,IAAAA,0BAAgB,EAACtC,QAAQ;QAC3C,MAAMuC,sBAAsBC,IAAAA,gCAAe,EAACH;QAE5C,IACE,CAACI,IAAAA,sCAAkB,EAACL,aACpB,CAACG,uBACD,mEAAmE;QACnE,uEAAuE;QACvE,wEAAwE;QACxE,iCAAiC;QACjCjC,aAAaoC,SAAS,KAAK,MAC3B;YACA,OAAO;gBAAEC,cAAc;oBAAEC,YAAY;oBAAGC,QAAQC;gBAAU;YAAE;QAC9D;QAEA,MAAMC,WAAW,MAAM9C,QAAO+C,MAAM,CAACvC,SAASO;QAE9C,MAAMiC,gBAAgBF,SAASG,MAAM,GAAG,OAAOH,SAASG,MAAM,KAAK;QACnE,IAAI,CAACD,eAAe;YAClB,OAAO;gBAAEN,cAAc;oBAAEC,YAAY;oBAAGC,QAAQC;gBAAU;YAAE;QAC9D;QAEA,MAAMK,OAAO,MAAMJ,SAASI,IAAI;QAEhC,gFAAgF;QAChF,2EAA2E;QAC3E,MAAMrC,YAAYsC,YAAY;QAE9B,MAAMR,aACJ,OAAO5B,QAAQU,UAAU,CAAC2B,mBAAmB,KAAK,eAClDrC,QAAQU,UAAU,CAAC2B,mBAAmB,IAAIC,yBAAc,GACpD,QACAtC,QAAQU,UAAU,CAAC2B,mBAAmB;QAE5C,MAAMR,SACJ,OAAO7B,QAAQU,UAAU,CAAC6B,eAAe,KAAK,eAC9CvC,QAAQU,UAAU,CAAC6B,eAAe,IAAID,yBAAc,GAChDR,YACA9B,QAAQU,UAAU,CAAC6B,eAAe;QAExC,MAAMC,UAAUC,IAAAA,gCAAyB,EAACV,SAASS,OAAO;QAC1D,MAAME,YAAY1C,QAAQU,UAAU,CAACiC,aAAa;QAElD,IAAID,WAAW;YACbF,OAAO,CAACI,iCAAsB,CAAC,GAAGF;QACpC;QAEA,IAAI,CAACF,OAAO,CAAC,eAAe,IAAIL,KAAKU,IAAI,EAAE;YACzCL,OAAO,CAAC,eAAe,GAAGL,KAAKU,IAAI;QACrC;QAEA,mCAAmC;QACnC,MAAMC,OAAOC,OAAOC,IAAI,CAAC,MAAMb,KAAKc,WAAW;QAC/C5D,WAAW6D,MAAM,CAAC9D,aAAa+D,OAAO,CAAC,WAAWC,2BAAgB,GAAGN;QAErE,wCAAwC;QACxC,MAAMO,OAAO;YAAEnB,QAAQH,SAASG,MAAM;YAAEM;QAAQ;QAChDnD,WAAW6D,MAAM,CACf9D,aAAa+D,OAAO,CAAC,WAAWG,2BAAgB,GAChDC,KAAKC,SAAS,CAACH;QAGjB,OAAO;YACL1B,cAAc;gBAAEC;gBAAYC;YAAO;YACnC4B,UAAUJ;QACZ;IACF,EAAE,OAAOK,KAAK;QACZ,IAAI,CAACC,IAAAA,wCAAmB,EAACD,MAAM;YAC7B,MAAMA;QACR;QAEA,OAAO;YAAE/B,cAAc;gBAAEC,YAAY;gBAAGC,QAAQC;YAAU;QAAE;IAC9D;AACF"}