import { NextApiRequest, NextApiResponse } from 'next';
import { getSiteSettings, updateSiteSettings } from '../../lib/database';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        const settings = getSiteSettings();
        res.status(200).json(settings);
        break;

      case 'PUT':
        const updates = req.body;
        updateSiteSettings(updates);
        const updatedSettings = getSiteSettings();
        res.status(200).json(updatedSettings);
        break;

      default:
        res.setHeader('Allow', ['GET', 'PUT']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
