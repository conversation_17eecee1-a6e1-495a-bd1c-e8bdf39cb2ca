import { NextApiRequest, NextApiResponse } from 'next';
import { getCategoryById, updateCategory, deleteCategory } from '../../../utils/database';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  if (typeof id !== 'string') {
    return res.status(400).json({ error: 'Invalid category ID' });
  }

  try {
    switch (req.method) {
      case 'GET':
        const category = getCategoryById(id);
        if (!category) {
          return res.status(404).json({ error: 'Category not found' });
        }
        res.status(200).json(category);
        break;

      case 'PUT':
        const updates = req.body;
        const updatedCategory = updateCategory(id, updates);
        if (!updatedCategory) {
          return res.status(404).json({ error: 'Category not found' });
        }
        res.status(200).json(updatedCategory);
        break;

      case 'DELETE':
        const deleted = deleteCategory(id);
        if (!deleted) {
          return res.status(404).json({ error: 'Category not found' });
        }
        res.status(200).json({ message: 'Category deleted successfully' });
        break;

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
