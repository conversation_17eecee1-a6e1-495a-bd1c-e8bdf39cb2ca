import { NextApiRequest, NextApiResponse } from 'next';
import { 
  getProducts, 
  addProduct, 
  updateProduct, 
  deleteProduct, 
  getProductById,
  getProductsByCategory,
  getProductsBySubcategory 
} from '../../../utils/database';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        const { categoryId, subcategoryId } = req.query;
        
        let products;
        if (subcategoryId && typeof subcategoryId === 'string') {
          products = getProductsBySubcategory(subcategoryId);
        } else if (categoryId && typeof categoryId === 'string') {
          products = getProductsByCategory(categoryId);
        } else {
          products = getProducts();
        }
        
        res.status(200).json({ success: true, data: products });
        break;

      case 'POST':
        const {
          title,
          titleAr,
          description,
          descriptionAr,
          images,
          price,
          originalPrice,
          available,
          categoryId: catId,
          subcategoryId: subCatId,
          features,
          featuresAr,
          specifications,
          isActive,
          isFeatured
        } = req.body;
        
        if (!title || !titleAr || !description || !descriptionAr || !catId || !subCatId || price === undefined) {
          return res.status(400).json({ 
            success: false, 
            message: 'Required fields are missing' 
          });
        }

        const newProduct = addProduct({
          title,
          titleAr,
          description,
          descriptionAr,
          images: images || [],
          price: parseFloat(price),
          originalPrice: originalPrice ? parseFloat(originalPrice) : undefined,
          available: available !== undefined ? available : true,
          categoryId: catId,
          subcategoryId: subCatId,
          features: features || [],
          featuresAr: featuresAr || [],
          specifications: specifications || [{ nameEn: '', nameAr: '', valueEn: '', valueAr: '' }],
          isActive: isActive !== undefined ? isActive : true,
          isFeatured: isFeatured !== undefined ? isFeatured : false
        });

        res.status(201).json({ success: true, data: newProduct });
        break;

      case 'PUT':
        const { id } = req.query;
        
        if (!id || typeof id !== 'string') {
          return res.status(400).json({ 
            success: false, 
            message: 'Product ID is required' 
          });
        }

        const existingProduct = getProductById(id);
        if (!existingProduct) {
          return res.status(404).json({ 
            success: false, 
            message: 'Product not found' 
          });
        }

        // تحويل الأسعار إلى أرقام إذا كانت موجودة
        const updateData = { ...req.body };
        if (updateData.price !== undefined) {
          updateData.price = parseFloat(updateData.price);
        }
        if (updateData.originalPrice !== undefined) {
          updateData.originalPrice = parseFloat(updateData.originalPrice);
        }

        const updatedProduct = updateProduct(id, updateData);
        res.status(200).json({ success: true, data: updatedProduct });
        break;

      case 'DELETE':
        const { id: deleteId } = req.query;
        
        if (!deleteId || typeof deleteId !== 'string') {
          return res.status(400).json({ 
            success: false, 
            message: 'Product ID is required' 
          });
        }

        const deleted = deleteProduct(deleteId);
        
        if (!deleted) {
          return res.status(404).json({ 
            success: false, 
            message: 'Product not found' 
          });
        }

        res.status(200).json({ 
          success: true, 
          message: 'Product deleted successfully' 
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        res.status(405).json({ 
          success: false, 
          message: `Method ${req.method} not allowed` 
        });
    }
  } catch (error) {
    console.error('Products API error:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
}
