'use client';

import { useParams } from 'next/navigation';
import { Locale } from '../../../lib/i18n';
import { getTranslation } from '../../../lib/translations';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import WhatsAppButton from '../../../components/WhatsAppButton';
import { useSiteSettings } from '../../../src/hooks/useSiteSettings';

export default function AboutPage() {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;
  const { settings, loading } = useSiteSettings();

  // استخدام الإعدادات من لوحة التحكم أو القيم الافتراضية
  const getAboutContent = () => {
    // القيم الافتراضية في حالة عدم وجود إعدادات
    const defaultContent = {
      ar: {
        title: 'من نحن',
        subtitle: 'تعرف على شركة دروب هاجر المتخصصة في توفير أفضل تجهيزات الفنادق والمطاعم',
        description: 'شركة دروب هاجر هي شركة رائدة في مجال توفير تجهيزات الفنادق والمطاعم بأعلى معايير الجودة والكفاءة.',
        vision: 'رؤيتنا',
        visionText: 'أن نكون الشركة الرائدة في المملكة العربية السعودية في مجال تجهيزات الفنادق والمطاعم.',
        mission: 'مهمتنا',
        missionText: 'نلتزم بتوفير معدات وتجهيزات عالية الجودة للفنادق والمطاعم.',
        values: 'قيمنا',
        valuesItems: [
          { title: 'الجودة', description: 'نلتزم بأعلى معايير الجودة في جميع منتجاتنا وخدماتنا', icon: 'ri-award-line' },
          { title: 'الابتكار', description: 'نسعى دائماً لتقديم حلول مبتكرة ومتطورة', icon: 'ri-lightbulb-line' },
          { title: 'الثقة', description: 'نبني علاقات طويلة الأمد مع عملائنا قائمة على الثقة والشفافية', icon: 'ri-shield-check-line' },
          { title: 'التميز', description: 'نسعى للتميز في كل ما نقوم به', icon: 'ri-star-line' }
        ],
        team: 'فريقنا',
        teamText: 'يضم فريقنا مجموعة من الخبراء والمتخصصين في مجال تجهيزات الفنادق والمطاعم.',
        contact: 'تواصل معنا',
        contactText: 'نحن هنا لخدمتكم ومساعدتكم في جميع احتياجاتكم'
      },
      en: {
        title: 'About Us',
        subtitle: 'Learn about Droob Hajer, specialized in providing the best hotel and restaurant equipment',
        description: 'Droob Hajer is a leading company in providing hotel and restaurant equipment with the highest standards of quality and efficiency.',
        vision: 'Our Vision',
        visionText: 'To be the leading company in Saudi Arabia in the field of hotel and restaurant equipment.',
        mission: 'Our Mission',
        missionText: 'We are committed to providing high-quality equipment and supplies for hotels and restaurants.',
        values: 'Our Values',
        valuesItems: [
          { title: 'Quality', description: 'We are committed to the highest quality standards in all our products and services', icon: 'ri-award-line' },
          { title: 'Innovation', description: 'We always strive to provide innovative and advanced solutions', icon: 'ri-lightbulb-line' },
          { title: 'Trust', description: 'We build long-term relationships with our customers based on trust and transparency', icon: 'ri-shield-check-line' },
          { title: 'Excellence', description: 'We strive for excellence in everything we do', icon: 'ri-star-line' }
        ],
        team: 'Our Team',
        teamText: 'Our team includes a group of experts and specialists in the field of hotel and restaurant equipment.',
        contact: 'Contact Us',
        contactText: 'We are here to serve you and help you with all your needs'
      }
    };

    // إذا لم تكن الإعدادات متاحة، استخدم القيم الافتراضية
    if (!settings?.aboutSettings) {
      return defaultContent[locale];
    }

    // استخدام الإعدادات من لوحة التحكم
    return {
      title: locale === 'ar'
        ? (settings.aboutSettings.heroSection.titleAr || defaultContent[locale].title)
        : (settings.aboutSettings.heroSection.title || defaultContent[locale].title),
      subtitle: locale === 'ar'
        ? (settings.aboutSettings.heroSection.subtitleAr || defaultContent[locale].subtitle)
        : (settings.aboutSettings.heroSection.subtitle || defaultContent[locale].subtitle),
      description: locale === 'ar'
        ? (settings.aboutSettings.description.textAr || defaultContent[locale].description)
        : (settings.aboutSettings.description.text || defaultContent[locale].description),
      vision: locale === 'ar'
        ? (settings.aboutSettings.vision.titleAr || defaultContent[locale].vision)
        : (settings.aboutSettings.vision.title || defaultContent[locale].vision),
      visionText: locale === 'ar'
        ? (settings.aboutSettings.vision.textAr || defaultContent[locale].visionText)
        : (settings.aboutSettings.vision.text || defaultContent[locale].visionText),
      mission: locale === 'ar'
        ? (settings.aboutSettings.mission.titleAr || defaultContent[locale].mission)
        : (settings.aboutSettings.mission.title || defaultContent[locale].mission),
      missionText: locale === 'ar'
        ? (settings.aboutSettings.mission.textAr || defaultContent[locale].missionText)
        : (settings.aboutSettings.mission.text || defaultContent[locale].missionText),
      values: locale === 'ar'
        ? (settings.aboutSettings.values.titleAr || defaultContent[locale].values)
        : (settings.aboutSettings.values.title || defaultContent[locale].values),
      valuesItems: settings.aboutSettings.values.items.length > 0
        ? settings.aboutSettings.values.items.map(item => ({
            title: locale === 'ar' ? item.titleAr : item.titleEn,
            description: locale === 'ar' ? item.descriptionAr : item.descriptionEn,
            icon: item.icon
          }))
        : defaultContent[locale].valuesItems,
      team: locale === 'ar'
        ? (settings.aboutSettings.team.titleAr || defaultContent[locale].team)
        : (settings.aboutSettings.team.title || defaultContent[locale].team),
      teamText: locale === 'ar'
        ? (settings.aboutSettings.team.descriptionAr || defaultContent[locale].teamText)
        : (settings.aboutSettings.team.description || defaultContent[locale].teamText),
      contact: locale === 'ar'
        ? (settings.aboutSettings.contactCTA.titleAr || defaultContent[locale].contact)
        : (settings.aboutSettings.contactCTA.title || defaultContent[locale].contact),
      contactText: locale === 'ar'
        ? (settings.aboutSettings.contactCTA.descriptionAr || defaultContent[locale].contactText)
        : (settings.aboutSettings.contactCTA.description || defaultContent[locale].contactText)
    };
  };

  const currentContent = getAboutContent();

  // عرض حالة التحميل
  if (loading) {
    return (
      <>
        <Navbar locale={locale} />
        <main className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <i className="ri-loader-4-line text-4xl text-primary animate-spin mb-4"></i>
            <p className="text-gray-600">
              {locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
            </p>
          </div>
        </main>
        <Footer locale={locale} />
      </>
    );
  }

  return (
    <>
      <Navbar locale={locale} />
      <main>
        {/* Hero Section */}
        <section className="bg-primary py-16">
          <div className="container mx-auto px-4">
            <div className="text-center text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {currentContent.title}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto">
                {currentContent.subtitle}
              </p>
            </div>
          </div>
        </section>

        {/* About Description */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <p className="text-lg text-gray-700 leading-relaxed">
                {currentContent.description}
              </p>
            </div>
          </div>
        </section>

        {/* Vision & Mission */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              {/* Vision */}
              <div className="bg-white rounded-xl p-8 shadow-lg">
                <div className="text-center mb-6">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i className="ri-eye-line text-2xl text-primary"></i>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800">
                    {currentContent.vision}
                  </h3>
                </div>
                <p className="text-gray-600 leading-relaxed">
                  {currentContent.visionText}
                </p>
              </div>

              {/* Mission */}
              <div className="bg-white rounded-xl p-8 shadow-lg">
                <div className="text-center mb-6">
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i className="ri-target-line text-2xl text-primary"></i>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800">
                    {currentContent.mission}
                  </h3>
                </div>
                <p className="text-gray-600 leading-relaxed">
                  {currentContent.missionText}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Values */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">
                {currentContent.values}
              </h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {currentContent.valuesItems.map((value, index) => (
                <div key={index} className="text-center">
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i className={`${value.icon || 'ri-star-line'} text-3xl text-primary`}></i>
                  </div>
                  <h4 className="text-xl font-bold text-gray-800 mb-2">
                    {value.title}
                  </h4>
                  <p className="text-gray-600">
                    {value.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">
                {currentContent.team}
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                {currentContent.teamText}
              </p>
            </div>
          </div>
        </section>



        {/* Contact CTA */}
        {(!settings?.aboutSettings?.contactCTA || settings.aboutSettings.contactCTA.enabled) && (
          <section className="py-16 bg-primary">
            <div className="container mx-auto px-4">
              <div className="text-center text-white">
                <h2 className="text-3xl font-bold mb-4">
                  {currentContent.contact}
                </h2>
                <p className="text-xl text-white/90 mb-8">
                  {currentContent.contactText}
                </p>
                <a
                  href={`/${locale}/contact`}
                  className="bg-white text-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center gap-2"
                >
                  <i className="ri-phone-line"></i>
                  {currentContent.contact}
                </a>
              </div>
            </div>
          </section>
        )}
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
