{"version": 3, "sources": ["../../../src/lib/typescript/getTypeScriptConfiguration.ts"], "sourcesContent": ["import { bold, cyan } from '../picocolors'\nimport os from 'os'\nimport path from 'path'\n\nimport { FatalError } from '../fatal-error'\nimport isError from '../is-error'\n\nexport async function getTypeScriptConfiguration(\n  ts: typeof import('typescript'),\n  tsConfigPath: string,\n  metaOnly?: boolean\n): Promise<import('typescript').ParsedCommandLine> {\n  try {\n    const formatDiagnosticsHost: import('typescript').FormatDiagnosticsHost = {\n      getCanonicalFileName: (fileName: string) => fileName,\n      getCurrentDirectory: ts.sys.getCurrentDirectory,\n      getNewLine: () => os.EOL,\n    }\n\n    const { config, error } = ts.readConfigFile(tsConfigPath, ts.sys.readFile)\n    if (error) {\n      throw new FatalError(ts.formatDiagnostic(error, formatDiagnosticsHost))\n    }\n\n    let configToParse: any = config\n\n    const result = ts.parseJsonConfigFileContent(\n      configToParse,\n      // When only interested in meta info,\n      // avoid enumerating all files (for performance reasons)\n      metaOnly\n        ? {\n            ...ts.sys,\n            readDirectory(_path, extensions, _excludes, _includes, _depth) {\n              return [extensions ? `file${extensions[0]}` : `file.ts`]\n            },\n          }\n        : ts.sys,\n      path.dirname(tsConfigPath)\n    )\n\n    if (result.errors) {\n      result.errors = result.errors.filter(\n        ({ code }) =>\n          // No inputs were found in config file\n          code !== 18003\n      )\n    }\n\n    if (result.errors?.length) {\n      throw new FatalError(\n        ts.formatDiagnostic(result.errors[0], formatDiagnosticsHost)\n      )\n    }\n\n    return result\n  } catch (err) {\n    if (isError(err) && err.name === 'SyntaxError') {\n      const reason = '\\n' + (err.message ?? '')\n      throw new FatalError(\n        bold(\n          'Could not parse' +\n            cyan('tsconfig.json') +\n            '.' +\n            ' Please make sure it contains syntactically correct JSON.'\n        ) + reason\n      )\n    }\n    throw err\n  }\n}\n"], "names": ["getTypeScriptConfiguration", "ts", "tsConfigPath", "metaOnly", "result", "formatDiagnosticsHost", "getCanonicalFileName", "fileName", "getCurrentDirectory", "sys", "getNewLine", "os", "EOL", "config", "error", "readConfigFile", "readFile", "FatalE<PERSON>r", "formatDiagnostic", "configToParse", "parseJsonConfigFileContent", "readDirectory", "_path", "extensions", "_excludes", "_includes", "_depth", "path", "dirname", "errors", "filter", "code", "length", "err", "isError", "name", "reason", "message", "bold", "cyan"], "mappings": ";;;;+BAOsBA;;;eAAAA;;;4BAPK;2DACZ;6DACE;4BAEU;gEACP;;;;;;AAEb,eAAeA,2BACpBC,EAA+B,EAC/BC,YAAoB,EACpBC,QAAkB;IAElB,IAAI;YAqCEC;QApCJ,MAAMC,wBAAoE;YACxEC,sBAAsB,CAACC,WAAqBA;YAC5CC,qBAAqBP,GAAGQ,GAAG,CAACD,mBAAmB;YAC/CE,YAAY,IAAMC,WAAE,CAACC,GAAG;QAC1B;QAEA,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE,GAAGb,GAAGc,cAAc,CAACb,cAAcD,GAAGQ,GAAG,CAACO,QAAQ;QACzE,IAAIF,OAAO;YACT,MAAM,qBAAiE,CAAjE,IAAIG,sBAAU,CAAChB,GAAGiB,gBAAgB,CAACJ,OAAOT,yBAA1C,qBAAA;uBAAA;4BAAA;8BAAA;YAAgE;QACxE;QAEA,IAAIc,gBAAqBN;QAEzB,MAAMT,SAASH,GAAGmB,0BAA0B,CAC1CD,eACA,qCAAqC;QACrC,wDAAwD;QACxDhB,WACI;YACE,GAAGF,GAAGQ,GAAG;YACTY,eAAcC,KAAK,EAAEC,UAAU,EAAEC,SAAS,EAAEC,SAAS,EAAEC,MAAM;gBAC3D,OAAO;oBAACH,aAAa,CAAC,IAAI,EAAEA,UAAU,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC;iBAAC;YAC1D;QACF,IACAtB,GAAGQ,GAAG,EACVkB,aAAI,CAACC,OAAO,CAAC1B;QAGf,IAAIE,OAAOyB,MAAM,EAAE;YACjBzB,OAAOyB,MAAM,GAAGzB,OAAOyB,MAAM,CAACC,MAAM,CAClC,CAAC,EAAEC,IAAI,EAAE,GACP,sCAAsC;gBACtCA,SAAS;QAEf;QAEA,KAAI3B,iBAAAA,OAAOyB,MAAM,qBAAbzB,eAAe4B,MAAM,EAAE;YACzB,MAAM,qBAEL,CAFK,IAAIf,sBAAU,CAClBhB,GAAGiB,gBAAgB,CAACd,OAAOyB,MAAM,CAAC,EAAE,EAAExB,yBADlC,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,OAAOD;IACT,EAAE,OAAO6B,KAAK;QACZ,IAAIC,IAAAA,gBAAO,EAACD,QAAQA,IAAIE,IAAI,KAAK,eAAe;YAC9C,MAAMC,SAAS,OAAQH,CAAAA,IAAII,OAAO,IAAI,EAAC;YACvC,MAAM,qBAOL,CAPK,IAAIpB,sBAAU,CAClBqB,IAAAA,gBAAI,EACF,oBACEC,IAAAA,gBAAI,EAAC,mBACL,MACA,+DACAH,SANA,qBAAA;uBAAA;4BAAA;8BAAA;YAON;QACF;QACA,MAAMH;IACR;AACF"}