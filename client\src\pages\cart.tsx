import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import Link from 'next/link';
import Image from 'next/image';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { GetStaticProps } from 'next';

interface CartItem {
  id: string;
  title: string;
  image: string;
  price: number;
  quantity: number;
}

const CartPage: React.FC = () => {
  const router = useRouter();
  const locale = (router.locale || 'ar') as 'ar' | 'en';
  const { t } = useTranslation('common');
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [form, setForm] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
  });

  useEffect(() => {
    // جلب المنتجات من localStorage
    const items = localStorage.getItem('cart');
    if (items) {
      try {
        setCartItems(JSON.parse(items));
      } catch {
        setCartItems([]);
      }
    }
  }, []);

  const updateCart = (newItems: CartItem[]) => {
    setCartItems(newItems);
    localStorage.setItem('cart', JSON.stringify(newItems));
    window.dispatchEvent(new Event('cartUpdated'));
  };

  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(id);
      return;
    }
    const updatedItems = cartItems.map(item =>
      item.id === id ? { ...item, quantity: newQuantity } : item
    );
    updateCart(updatedItems);
  };

  const removeItem = (id: string) => {
    const updatedItems = cartItems.filter(item => item.id !== id);
    updateCart(updatedItems);
  };

  const clearCart = () => {
    updateCart([]);
  };

  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSend = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // إرسال البيانات إلى API
      const response = await fetch('/api/quote-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerInfo: form,
          products: cartItems.map(item => ({
            ...item,
            titleAr: item.title // إضافة العنوان العربي
          }))
        }),
      });

      const result = await response.json();

      if (result.success) {
        alert(t('cart.orderSent'));
        setShowForm(false);
        setForm({ name: '', email: '', phone: '', company: '' });
        clearCart();
      } else {
        alert('حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
      }
    } catch (error) {
      console.error('Error sending quote request:', error);
      alert('حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
    }
  };

  return (
    <>
      <Navbar locale={locale} />
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">{t('cart.title')}</h1>
            <p className="text-gray-600">{t('cart.subtitle')}</p>
          </div>

          {cartItems.length === 0 ? (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <i className="ri-shopping-cart-2-line text-6xl text-gray-400 mb-4"></i>
                <h2 className="text-2xl font-bold text-gray-800 mb-4">{t('cart.empty')}</h2>
                <p className="text-gray-600 mb-6">{t('cart.emptyMessage')}</p>
                <Link href="/products" className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors inline-flex items-center gap-2">
                  <i className="ri-arrow-right-line"></i>
                  {t('cart.browseProducts')}
                </Link>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* قائمة المنتجات */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex justify-between items-center">
                      <h2 className="text-xl font-bold text-gray-800">{t('cart.selectedProducts')} ({cartItems.length})</h2>
                      <button
                        onClick={clearCart}
                        className="text-red-500 hover:text-red-700 font-medium transition-colors"
                      >
                        {t('cart.clearAll')}
                      </button>
                    </div>
                  </div>
                  <div className="divide-y divide-gray-200">
                    {cartItems.map((item) => (
                      <div key={item.id} className="p-6 hover:bg-gray-50 transition-colors">
                        <div className="flex items-center gap-4">

                          <Image
                            src={item.image}
                            alt={item.title}
                            width={80}
                            height={80}
                            className="w-20 h-20 object-cover rounded-lg shadow-md"
                          />
                          <div className="flex-1">
                            <h3 className="font-bold text-gray-800 mb-1">{item.title}</h3>
                            <p className="text-primary font-semibold">{item.price} {t('cart.currency')}</p>
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="flex items-center border border-gray-300 rounded-lg">
                              <button
                                onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                className="px-3 py-2 hover:bg-gray-100 transition-colors"
                              >
                                <i className="ri-subtract-line"></i>
                              </button>
                              <span className="px-4 py-2 border-x border-gray-300 min-w-[60px] text-center">{item.quantity}</span>
                              <button
                                onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                className="px-3 py-2 hover:bg-gray-100 transition-colors"
                              >
                                <i className="ri-add-line"></i>
                              </button>
                            </div>
                            <button
                              onClick={() => removeItem(item.id)}
                              className="text-red-500 hover:text-red-700 p-2 transition-colors"
                              title={t('cart.deleteProduct')}
                            >
                              <i className="ri-delete-bin-line"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* ملخص الطلب */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-xl shadow-lg p-6 sticky top-24">
                  <h2 className="text-xl font-bold text-gray-800 mb-6">{t('cart.orderSummary')}</h2>

                  <div className="space-y-4 mb-6">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">{t('cart.productCount')}</span>
                      <span className="font-semibold">{cartItems.reduce((sum, item) => sum + item.quantity, 0)}</span>
                    </div>
                    <div className="flex justify-between items-center text-lg font-bold text-primary border-t border-gray-200 pt-4">
                      <span>{t('cart.total')}</span>
                      <span>{getTotalPrice()} {t('cart.currency')}</span>
                    </div>
                  </div>

                  <button
                    onClick={() => setShowForm(true)}
                    className="w-full bg-primary text-white py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors mb-4"
                  >
                    {t('cart.requestQuote')}
                  </button>

                  <Link
                    href="/products"
                    className="w-full bg-gray-100 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors text-center block"
                  >
                    {t('cart.continueShopping')}
                  </Link>
                </div>
              </div>
            </div>
          )}

          {/* نموذج طلب عرض السعر */}
          {showForm && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                <div className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h3 className="text-xl font-bold text-gray-800">{t('cart.contactInfo')}</h3>
                    <button
                      onClick={() => setShowForm(false)}
                      className="text-gray-500 hover:text-gray-700 transition-colors"
                    >
                      <i className="ri-close-line text-xl"></i>
                    </button>
                  </div>

                  <form onSubmit={handleSend} className="space-y-4">
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">{t('cart.fullName')} *</label>
                      <input
                        name="name"
                        value={form.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                        placeholder={t('cart.fullNamePlaceholder')}
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">{t('cart.email')} *</label>
                      <input
                        type="email"
                        name="email"
                        value={form.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                        placeholder={t('cart.emailPlaceholder')}
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">{t('cart.phone')} *</label>
                      <input
                        name="phone"
                        value={form.phone}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                        placeholder={t('cart.phonePlaceholder')}
                      />
                    </div>
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">{t('cart.company')}</label>
                      <input
                        name="company"
                        value={form.company}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                        placeholder={t('cart.companyPlaceholder')}
                      />
                    </div>
                    <div className="flex gap-3 pt-4">
                      <button
                        type="button"
                        onClick={() => setShowForm(false)}
                        className="flex-1 bg-gray-100 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-200 transition-colors"
                      >
                        {t('cart.cancel')}
                      </button>
                      <button
                        type="submit"
                        className="flex-1 bg-primary text-white py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
                      >
                        {t('cart.sendOrder')}
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <Footer locale={locale} />
    </>
  );
};

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ar', ['common'])),
    },
  };
};

export default CartPage;
