{"version": 3, "sources": ["../../../src/server/app-render/required-scripts.tsx"], "sourcesContent": ["import { encodeURIPath } from '../../shared/lib/encode-uri-path'\nimport type { BuildManifest } from '../get-page-files'\n\nimport ReactDOM from 'react-dom'\n\nexport function getRequiredScripts(\n  buildManifest: BuildManifest,\n  assetPrefix: string,\n  crossOrigin: undefined | '' | 'anonymous' | 'use-credentials',\n  SRIManifest: undefined | Record<string, string>,\n  qs: string,\n  nonce: string | undefined,\n  pagePath: string\n): [\n  () => void,\n  { src: string; integrity?: string; crossOrigin?: string | undefined },\n] {\n  let preinitScripts: () => void\n  let preinitScriptCommands: string[] = []\n  const bootstrapScript: {\n    src: string\n    integrity?: string\n    crossOrigin?: string | undefined\n  } = {\n    src: '',\n    crossOrigin,\n  }\n\n  const files = (\n    buildManifest.rootMainFilesTree?.[pagePath] || buildManifest.rootMainFiles\n  ).map(encodeURIPath)\n  if (files.length === 0) {\n    throw new Error(\n      'Invariant: missing bootstrap script. This is a bug in Next.js'\n    )\n  }\n  if (SRIManifest) {\n    bootstrapScript.src = `${assetPrefix}/_next/` + files[0] + qs\n    bootstrapScript.integrity = SRIManifest[files[0]]\n\n    for (let i = 1; i < files.length; i++) {\n      const src = `${assetPrefix}/_next/` + files[i] + qs\n      const integrity = SRIManifest[files[i]]\n      preinitScriptCommands.push(src, integrity)\n    }\n    preinitScripts = () => {\n      // preinitScriptCommands is a double indexed array of src/integrity pairs\n      for (let i = 0; i < preinitScriptCommands.length; i += 2) {\n        ReactDOM.preinit(preinitScriptCommands[i], {\n          as: 'script',\n          integrity: preinitScriptCommands[i + 1],\n          crossOrigin,\n          nonce,\n        })\n      }\n    }\n  } else {\n    bootstrapScript.src = `${assetPrefix}/_next/` + files[0] + qs\n\n    for (let i = 1; i < files.length; i++) {\n      const src = `${assetPrefix}/_next/` + files[i] + qs\n      preinitScriptCommands.push(src)\n    }\n    preinitScripts = () => {\n      // preinitScriptCommands is a singled indexed array of src values\n      for (let i = 0; i < preinitScriptCommands.length; i++) {\n        ReactDOM.preinit(preinitScriptCommands[i], {\n          as: 'script',\n          nonce,\n          crossOrigin,\n        })\n      }\n    }\n  }\n\n  return [preinitScripts, bootstrapScript]\n}\n"], "names": ["encodeURIPath", "ReactDOM", "getRequiredScripts", "buildManifest", "assetPrefix", "crossOrigin", "SRIManifest", "qs", "nonce", "pagePath", "preinitScripts", "preinitScriptCommands", "bootstrapScript", "src", "files", "rootMainFilesTree", "rootMainFiles", "map", "length", "Error", "integrity", "i", "push", "preinit", "as"], "mappings": "AAAA,SAASA,aAAa,QAAQ,mCAAkC;AAGhE,OAAOC,cAAc,YAAW;AAEhC,OAAO,SAASC,mBACdC,aAA4B,EAC5BC,WAAmB,EACnBC,WAA6D,EAC7DC,WAA+C,EAC/CC,EAAU,EACVC,KAAyB,EACzBC,QAAgB;QAiBdN;IAZF,IAAIO;IACJ,IAAIC,wBAAkC,EAAE;IACxC,MAAMC,kBAIF;QACFC,KAAK;QACLR;IACF;IAEA,MAAMS,QAAQ,AACZX,CAAAA,EAAAA,mCAAAA,cAAcY,iBAAiB,qBAA/BZ,gCAAiC,CAACM,SAAS,KAAIN,cAAca,aAAa,AAAD,EACzEC,GAAG,CAACjB;IACN,IAAIc,MAAMI,MAAM,KAAK,GAAG;QACtB,MAAM,qBAEL,CAFK,IAAIC,MACR,kEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACA,IAAIb,aAAa;QACfM,gBAAgBC,GAAG,GAAG,GAAGT,YAAY,OAAO,CAAC,GAAGU,KAAK,CAAC,EAAE,GAAGP;QAC3DK,gBAAgBQ,SAAS,GAAGd,WAAW,CAACQ,KAAK,CAAC,EAAE,CAAC;QAEjD,IAAK,IAAIO,IAAI,GAAGA,IAAIP,MAAMI,MAAM,EAAEG,IAAK;YACrC,MAAMR,MAAM,GAAGT,YAAY,OAAO,CAAC,GAAGU,KAAK,CAACO,EAAE,GAAGd;YACjD,MAAMa,YAAYd,WAAW,CAACQ,KAAK,CAACO,EAAE,CAAC;YACvCV,sBAAsBW,IAAI,CAACT,KAAKO;QAClC;QACAV,iBAAiB;YACf,yEAAyE;YACzE,IAAK,IAAIW,IAAI,GAAGA,IAAIV,sBAAsBO,MAAM,EAAEG,KAAK,EAAG;gBACxDpB,SAASsB,OAAO,CAACZ,qBAAqB,CAACU,EAAE,EAAE;oBACzCG,IAAI;oBACJJ,WAAWT,qBAAqB,CAACU,IAAI,EAAE;oBACvChB;oBACAG;gBACF;YACF;QACF;IACF,OAAO;QACLI,gBAAgBC,GAAG,GAAG,GAAGT,YAAY,OAAO,CAAC,GAAGU,KAAK,CAAC,EAAE,GAAGP;QAE3D,IAAK,IAAIc,IAAI,GAAGA,IAAIP,MAAMI,MAAM,EAAEG,IAAK;YACrC,MAAMR,MAAM,GAAGT,YAAY,OAAO,CAAC,GAAGU,KAAK,CAACO,EAAE,GAAGd;YACjDI,sBAAsBW,IAAI,CAACT;QAC7B;QACAH,iBAAiB;YACf,iEAAiE;YACjE,IAAK,IAAIW,IAAI,GAAGA,IAAIV,sBAAsBO,MAAM,EAAEG,IAAK;gBACrDpB,SAASsB,OAAO,CAACZ,qBAAqB,CAACU,EAAE,EAAE;oBACzCG,IAAI;oBACJhB;oBACAH;gBACF;YACF;QACF;IACF;IAEA,OAAO;QAACK;QAAgBE;KAAgB;AAC1C"}