{"version": 3, "sources": ["../../../src/shared/lib/constants.ts"], "sourcesContent": ["import MODERN_BROWSERSLIST_TARGET from './modern-browserslist-target'\n\nexport { MODERN_BROWSERSLIST_TARGET }\n\nexport type ValueOf<T> = Required<T>[keyof T]\n\nexport const COMPILER_NAMES = {\n  client: 'client',\n  server: 'server',\n  edgeServer: 'edge-server',\n} as const\n\nexport type CompilerNameValues = ValueOf<typeof COMPILER_NAMES>\n\nexport const COMPILER_INDEXES: {\n  [compilerKey in CompilerNameValues]: number\n} = {\n  [COMPILER_NAMES.client]: 0,\n  [COMPILER_NAMES.server]: 1,\n  [COMPILER_NAMES.edgeServer]: 2,\n} as const\n\nexport const UNDERSCORE_NOT_FOUND_ROUTE = '/_not-found'\nexport const UNDERSCORE_NOT_FOUND_ROUTE_ENTRY = `${UNDERSCORE_NOT_FOUND_ROUTE}/page`\nexport const PHASE_EXPORT = 'phase-export'\nexport const PHASE_PRODUCTION_BUILD = 'phase-production-build'\nexport const PHASE_PRODUCTION_SERVER = 'phase-production-server'\nexport const PHASE_DEVELOPMENT_SERVER = 'phase-development-server'\nexport const PHASE_TEST = 'phase-test'\nexport const PHASE_INFO = 'phase-info'\nexport const PAGES_MANIFEST = 'pages-manifest.json'\nexport const WEBPACK_STATS = 'webpack-stats.json'\nexport const APP_PATHS_MANIFEST = 'app-paths-manifest.json'\nexport const APP_PATH_ROUTES_MANIFEST = 'app-path-routes-manifest.json'\nexport const BUILD_MANIFEST = 'build-manifest.json'\nexport const APP_BUILD_MANIFEST = 'app-build-manifest.json'\nexport const FUNCTIONS_CONFIG_MANIFEST = 'functions-config-manifest.json'\nexport const SUBRESOURCE_INTEGRITY_MANIFEST = 'subresource-integrity-manifest'\nexport const NEXT_FONT_MANIFEST = 'next-font-manifest'\nexport const EXPORT_MARKER = 'export-marker.json'\nexport const EXPORT_DETAIL = 'export-detail.json'\nexport const PRERENDER_MANIFEST = 'prerender-manifest.json'\nexport const ROUTES_MANIFEST = 'routes-manifest.json'\nexport const IMAGES_MANIFEST = 'images-manifest.json'\nexport const SERVER_FILES_MANIFEST = 'required-server-files.json'\nexport const DEV_CLIENT_PAGES_MANIFEST = '_devPagesManifest.json'\nexport const MIDDLEWARE_MANIFEST = 'middleware-manifest.json'\nexport const TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST =\n  '_clientMiddlewareManifest.json'\nexport const DEV_CLIENT_MIDDLEWARE_MANIFEST = '_devMiddlewareManifest.json'\nexport const REACT_LOADABLE_MANIFEST = 'react-loadable-manifest.json'\nexport const SERVER_DIRECTORY = 'server'\nexport const CONFIG_FILES = [\n  'next.config.js',\n  'next.config.mjs',\n  'next.config.ts',\n]\nexport const BUILD_ID_FILE = 'BUILD_ID'\nexport const BLOCKED_PAGES = ['/_document', '/_app', '/_error']\nexport const CLIENT_PUBLIC_FILES_PATH = 'public'\nexport const CLIENT_STATIC_FILES_PATH = 'static'\nexport const STRING_LITERAL_DROP_BUNDLE = '__NEXT_DROP_CLIENT_FILE__'\nexport const NEXT_BUILTIN_DOCUMENT = '__NEXT_BUILTIN_DOCUMENT__'\nexport const BARREL_OPTIMIZATION_PREFIX = '__barrel_optimize__'\n\n// server/[entry]/page_client-reference-manifest.js\nexport const CLIENT_REFERENCE_MANIFEST = 'client-reference-manifest'\n// server/server-reference-manifest\nexport const SERVER_REFERENCE_MANIFEST = 'server-reference-manifest'\n// server/middleware-build-manifest.js\nexport const MIDDLEWARE_BUILD_MANIFEST = 'middleware-build-manifest'\n// server/middleware-react-loadable-manifest.js\nexport const MIDDLEWARE_REACT_LOADABLE_MANIFEST =\n  'middleware-react-loadable-manifest'\n// server/interception-route-rewrite-manifest.js\nexport const INTERCEPTION_ROUTE_REWRITE_MANIFEST =\n  'interception-route-rewrite-manifest'\n// server/dynamic-css-manifest.js\nexport const DYNAMIC_CSS_MANIFEST = 'dynamic-css-manifest'\n\n// static/runtime/main.js\nexport const CLIENT_STATIC_FILES_RUNTIME_MAIN = `main`\nexport const CLIENT_STATIC_FILES_RUNTIME_MAIN_APP = `${CLIENT_STATIC_FILES_RUNTIME_MAIN}-app`\n// next internal client components chunk for layouts\nexport const APP_CLIENT_INTERNALS = 'app-pages-internals'\n// static/runtime/react-refresh.js\nexport const CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH = `react-refresh`\n// static/runtime/amp.js\nexport const CLIENT_STATIC_FILES_RUNTIME_AMP = `amp`\n// static/runtime/webpack.js\nexport const CLIENT_STATIC_FILES_RUNTIME_WEBPACK = `webpack`\n// static/runtime/polyfills.js\nexport const CLIENT_STATIC_FILES_RUNTIME_POLYFILLS = 'polyfills'\nexport const CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL = Symbol(\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS\n)\nexport const DEFAULT_RUNTIME_WEBPACK = 'webpack-runtime'\nexport const EDGE_RUNTIME_WEBPACK = 'edge-runtime-webpack'\nexport const STATIC_PROPS_ID = '__N_SSG'\nexport const SERVER_PROPS_ID = '__N_SSP'\nexport const DEFAULT_SERIF_FONT = {\n  name: 'Times New Roman',\n  xAvgCharWidth: 821,\n  azAvgWidth: 854.3953488372093,\n  unitsPerEm: 2048,\n}\nexport const DEFAULT_SANS_SERIF_FONT = {\n  name: 'Arial',\n  xAvgCharWidth: 904,\n  azAvgWidth: 934.5116279069767,\n  unitsPerEm: 2048,\n}\nexport const STATIC_STATUS_PAGES = ['/500']\nexport const TRACE_OUTPUT_VERSION = 1\n// in `MB`\nexport const TURBO_TRACE_DEFAULT_MEMORY_LIMIT = 6000\n\nexport const RSC_MODULE_TYPES = {\n  client: 'client',\n  server: 'server',\n} as const\n\n// comparing\n// https://nextjs.org/docs/api-reference/edge-runtime\n// with\n// https://nodejs.org/docs/latest/api/globals.html\nexport const EDGE_UNSUPPORTED_NODE_APIS = [\n  'clearImmediate',\n  'setImmediate',\n  'BroadcastChannel',\n  'ByteLengthQueuingStrategy',\n  'CompressionStream',\n  'CountQueuingStrategy',\n  'DecompressionStream',\n  'DomException',\n  'MessageChannel',\n  'MessageEvent',\n  'MessagePort',\n  'ReadableByteStreamController',\n  'ReadableStreamBYOBRequest',\n  'ReadableStreamDefaultController',\n  'TransformStreamDefaultController',\n  'WritableStreamDefaultController',\n]\n\nexport const SYSTEM_ENTRYPOINTS = new Set<string>([\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n])\n"], "names": ["MODERN_BROWSERSLIST_TARGET", "COMPILER_NAMES", "client", "server", "edgeServer", "COMPILER_INDEXES", "UNDERSCORE_NOT_FOUND_ROUTE", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "PHASE_EXPORT", "PHASE_PRODUCTION_BUILD", "PHASE_PRODUCTION_SERVER", "PHASE_DEVELOPMENT_SERVER", "PHASE_TEST", "PHASE_INFO", "PAGES_MANIFEST", "WEBPACK_STATS", "APP_PATHS_MANIFEST", "APP_PATH_ROUTES_MANIFEST", "BUILD_MANIFEST", "APP_BUILD_MANIFEST", "FUNCTIONS_CONFIG_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "NEXT_FONT_MANIFEST", "EXPORT_MARKER", "EXPORT_DETAIL", "PRERENDER_MANIFEST", "ROUTES_MANIFEST", "IMAGES_MANIFEST", "SERVER_FILES_MANIFEST", "DEV_CLIENT_PAGES_MANIFEST", "MIDDLEWARE_MANIFEST", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "REACT_LOADABLE_MANIFEST", "SERVER_DIRECTORY", "CONFIG_FILES", "BUILD_ID_FILE", "BLOCKED_PAGES", "CLIENT_PUBLIC_FILES_PATH", "CLIENT_STATIC_FILES_PATH", "STRING_LITERAL_DROP_BUNDLE", "NEXT_BUILTIN_DOCUMENT", "BARREL_OPTIMIZATION_PREFIX", "CLIENT_REFERENCE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "INTERCEPTION_ROUTE_REWRITE_MANIFEST", "DYNAMIC_CSS_MANIFEST", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "APP_CLIENT_INTERNALS", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "Symbol", "DEFAULT_RUNTIME_WEBPACK", "EDGE_RUNTIME_WEBPACK", "STATIC_PROPS_ID", "SERVER_PROPS_ID", "DEFAULT_SERIF_FONT", "name", "xAvgCharWidth", "azAvgWidth", "unitsPerEm", "DEFAULT_SANS_SERIF_FONT", "STATIC_STATUS_PAGES", "TRACE_OUTPUT_VERSION", "TURBO_TRACE_DEFAULT_MEMORY_LIMIT", "RSC_MODULE_TYPES", "EDGE_UNSUPPORTED_NODE_APIS", "SYSTEM_ENTRYPOINTS", "Set"], "mappings": "AAAA,OAAOA,gCAAgC,+BAA8B;AAErE,SAASA,0BAA0B,GAAE;AAIrC,OAAO,MAAMC,iBAAiB;IAC5BC,QAAQ;IACRC,QAAQ;IACRC,YAAY;AACd,EAAU;AAIV,OAAO,MAAMC,mBAET;IACF,CAACJ,eAAeC,MAAM,CAAC,EAAE;IACzB,CAACD,eAAeE,MAAM,CAAC,EAAE;IACzB,CAACF,eAAeG,UAAU,CAAC,EAAE;AAC/B,EAAU;AAEV,OAAO,MAAME,6BAA6B,cAAa;AACvD,OAAO,MAAMC,mCAAmC,AAAC,KAAED,6BAA2B,QAAM;AACpF,OAAO,MAAME,eAAe,eAAc;AAC1C,OAAO,MAAMC,yBAAyB,yBAAwB;AAC9D,OAAO,MAAMC,0BAA0B,0BAAyB;AAChE,OAAO,MAAMC,2BAA2B,2BAA0B;AAClE,OAAO,MAAMC,aAAa,aAAY;AACtC,OAAO,MAAMC,aAAa,aAAY;AACtC,OAAO,MAAMC,iBAAiB,sBAAqB;AACnD,OAAO,MAAMC,gBAAgB,qBAAoB;AACjD,OAAO,MAAMC,qBAAqB,0BAAyB;AAC3D,OAAO,MAAMC,2BAA2B,gCAA+B;AACvE,OAAO,MAAMC,iBAAiB,sBAAqB;AACnD,OAAO,MAAMC,qBAAqB,0BAAyB;AAC3D,OAAO,MAAMC,4BAA4B,iCAAgC;AACzE,OAAO,MAAMC,iCAAiC,iCAAgC;AAC9E,OAAO,MAAMC,qBAAqB,qBAAoB;AACtD,OAAO,MAAMC,gBAAgB,qBAAoB;AACjD,OAAO,MAAMC,gBAAgB,qBAAoB;AACjD,OAAO,MAAMC,qBAAqB,0BAAyB;AAC3D,OAAO,MAAMC,kBAAkB,uBAAsB;AACrD,OAAO,MAAMC,kBAAkB,uBAAsB;AACrD,OAAO,MAAMC,wBAAwB,6BAA4B;AACjE,OAAO,MAAMC,4BAA4B,yBAAwB;AACjE,OAAO,MAAMC,sBAAsB,2BAA0B;AAC7D,OAAO,MAAMC,uCACX,iCAAgC;AAClC,OAAO,MAAMC,iCAAiC,8BAA6B;AAC3E,OAAO,MAAMC,0BAA0B,+BAA8B;AACrE,OAAO,MAAMC,mBAAmB,SAAQ;AACxC,OAAO,MAAMC,eAAe;IAC1B;IACA;IACA;CACD,CAAA;AACD,OAAO,MAAMC,gBAAgB,WAAU;AACvC,OAAO,MAAMC,gBAAgB;IAAC;IAAc;IAAS;CAAU,CAAA;AAC/D,OAAO,MAAMC,2BAA2B,SAAQ;AAChD,OAAO,MAAMC,2BAA2B,SAAQ;AAChD,OAAO,MAAMC,6BAA6B,4BAA2B;AACrE,OAAO,MAAMC,wBAAwB,4BAA2B;AAChE,OAAO,MAAMC,6BAA6B,sBAAqB;AAE/D,mDAAmD;AACnD,OAAO,MAAMC,4BAA4B,4BAA2B;AACpE,mCAAmC;AACnC,OAAO,MAAMC,4BAA4B,4BAA2B;AACpE,sCAAsC;AACtC,OAAO,MAAMC,4BAA4B,4BAA2B;AACpE,+CAA+C;AAC/C,OAAO,MAAMC,qCACX,qCAAoC;AACtC,gDAAgD;AAChD,OAAO,MAAMC,sCACX,sCAAqC;AACvC,iCAAiC;AACjC,OAAO,MAAMC,uBAAuB,uBAAsB;AAE1D,yBAAyB;AACzB,OAAO,MAAMC,mCAAoC,OAAK;AACtD,OAAO,MAAMC,uCAAuC,AAAC,KAAED,mCAAiC,OAAK;AAC7F,oDAAoD;AACpD,OAAO,MAAME,uBAAuB,sBAAqB;AACzD,kCAAkC;AAClC,OAAO,MAAMC,4CAA6C,gBAAc;AACxE,wBAAwB;AACxB,OAAO,MAAMC,kCAAmC,MAAI;AACpD,4BAA4B;AAC5B,OAAO,MAAMC,sCAAuC,UAAQ;AAC5D,8BAA8B;AAC9B,OAAO,MAAMC,wCAAwC,YAAW;AAChE,OAAO,MAAMC,+CAA+CC,OAC1DF,uCACD;AACD,OAAO,MAAMG,0BAA0B,kBAAiB;AACxD,OAAO,MAAMC,uBAAuB,uBAAsB;AAC1D,OAAO,MAAMC,kBAAkB,UAAS;AACxC,OAAO,MAAMC,kBAAkB,UAAS;AACxC,OAAO,MAAMC,qBAAqB;IAChCC,MAAM;IACNC,eAAe;IACfC,YAAY;IACZC,YAAY;AACd,EAAC;AACD,OAAO,MAAMC,0BAA0B;IACrCJ,MAAM;IACNC,eAAe;IACfC,YAAY;IACZC,YAAY;AACd,EAAC;AACD,OAAO,MAAME,sBAAsB;IAAC;CAAO,CAAA;AAC3C,OAAO,MAAMC,uBAAuB,EAAC;AACrC,UAAU;AACV,OAAO,MAAMC,mCAAmC,KAAI;AAEpD,OAAO,MAAMC,mBAAmB;IAC9BrE,QAAQ;IACRC,QAAQ;AACV,EAAU;AAEV,YAAY;AACZ,qDAAqD;AACrD,OAAO;AACP,kDAAkD;AAClD,OAAO,MAAMqE,6BAA6B;IACxC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAA;AAED,OAAO,MAAMC,qBAAqB,IAAIC,IAAY;IAChDzB;IACAG;IACAC;IACAH;CACD,EAAC"}