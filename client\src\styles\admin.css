/* تحسينات إضافية للوحة التحكم */

/* تأثيرات الانتقال العامة */
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تأثيرات الأزرار المتقدمة */
.admin-button {
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
}

.admin-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.admin-button:hover::before {
  left: 100%;
}

/* تأثيرات البطاقات */
.admin-card {
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.admin-card:hover {
  transform: translateY(-8px) rotateX(5deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* تأثيرات الأيقونات */
.admin-icon {
  transition: transform 0.3s ease, color 0.3s ease;
}

.admin-icon:hover {
  transform: scale(1.2) rotate(10deg);
}

/* تأثيرات التدرج المتحرك */
.gradient-animation {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* تأثيرات النبض */
.pulse-effect {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* تأثيرات الظهور */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تأثيرات الشريط الجانبي */
.sidebar-item {
  position: relative;
  overflow: hidden;
}

.sidebar-item::after {
  content: '';
  position: absolute;
  top: 0;
  right: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(45, 55, 72, 0.1), transparent);
  transition: right 0.5s;
}

.sidebar-item:hover::after {
  right: 100%;
}

/* تأثيرات الإحصائيات */
.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  transform: scale(0);
  transition: transform 0.5s ease;
}

.stats-card:hover::before {
  transform: scale(1);
}

/* تأثيرات النماذج */
.form-input {
  position: relative;
  transition: all 0.3s ease;
}

.form-input:focus {
  transform: scale(1.02);
  box-shadow: 0 0 0 3px rgba(45, 55, 72, 0.1);
}

/* تأثيرات التبويبات */
.tab-button {
  position: relative;
  overflow: hidden;
}

.tab-button::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(90deg, #2D3748, #4A5568);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.tab-button.active::before,
.tab-button:hover::before {
  width: 100%;
}

/* تأثيرات الجداول */
.table-row {
  transition: all 0.3s ease;
}

.table-row:hover {
  transform: scale(1.01);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* تأثيرات التحميل */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* تأثيرات النجاح */
.success-animation {
  animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* تأثيرات الخطأ */
.error-shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .admin-card:hover {
    transform: translateY(-4px);
  }
  
  .stats-card {
    margin-bottom: 1rem;
  }
}

/* تأثيرات الخلفية المتحركة */
.animated-background {
  background: linear-gradient(-45deg, #f8fafc, #f1f5f9, #e2e8f0, #cbd5e1);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

/* تحسينات إمكانية الوصول */
.focus-visible:focus {
  outline: 2px solid #2D3748;
  outline-offset: 2px;
}

/* تأثيرات الظلال المتقدمة */
.shadow-glow {
  box-shadow: 0 0 20px rgba(45, 55, 72, 0.3);
}

.shadow-glow:hover {
  box-shadow: 0 0 30px rgba(45, 55, 72, 0.4);
}
