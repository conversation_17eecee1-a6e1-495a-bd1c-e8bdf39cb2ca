'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Locale } from '../../../lib/i18n';
import { getTranslation } from '../../../lib/translations';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import ProductCard from '../../../components/ProductCard';
import WhatsAppButton from '../../../components/WhatsAppButton';
import { Product, Category } from '../../../src/types/database';
import { productsApi, categoriesApi } from '../../../src/lib/api';

export default function ProductsPage() {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const t = (key: string) => getTranslation(locale, key as any);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [productsData, categoriesData] = await Promise.all([
          productsApi.getAll(),
          categoriesApi.getAll()
        ]);
        setProducts(productsData);
        setCategories(categoriesData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const filteredProducts = products.filter(product => {
    const matchesCategory = selectedCategory === 'all' || product.categoryId === selectedCategory;
    const searchField = locale === 'ar' ? product.titleAr : product.title;
    const matchesSearch = searchField.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch && product.isActive;
  });

  return (
    <>
      <Navbar locale={locale} />
      <main>
        {/* Page Header */}
        <section className="bg-primary py-12">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl md:text-4xl font-bold text-white text-center">
              {t('products')}
            </h1>
            <p className="text-white/80 text-center mt-4 max-w-2xl mx-auto">
              {locale === 'ar' 
                ? 'اكتشف مجموعتنا الواسعة من معدات المطاعم والفنادق عالية الجودة'
                : 'Discover our wide range of high-quality restaurant and hotel equipment'
              }
            </p>
          </div>
        </section>

        {/* Products Section */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            {/* Filters */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6 mb-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('search')}:
                  </label>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder={locale === 'ar' ? 'ابحث عن منتج...' : 'Search for a product...'}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('categories')}:
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">
                      {locale === 'ar' ? 'جميع الفئات' : 'All Categories'}
                    </option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {locale === 'ar' ? category.nameAr : category.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Products Grid */}
            {loading ? (
              <div className="text-center py-16">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-gray-600">{t('loading')}</p>
              </div>
            ) : filteredProducts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                {filteredProducts.map((product) => (
                  <ProductCard
                    key={product.id}
                    id={parseInt(product.id)}
                    image={product.images[0]}
                    title={locale === 'ar' ? product.titleAr : product.title}
                    description={locale === 'ar' ? product.descriptionAr : product.description}
                    price={product.price}
                    available={product.available}
                    locale={locale}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <i className="ri-search-line text-6xl text-gray-400 mb-4"></i>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  {locale === 'ar' ? 'لا توجد منتجات' : 'No products found'}
                </h3>
                <p className="text-gray-600">
                  {locale === 'ar' 
                    ? 'جرب تغيير معايير البحث أو الفلترة'
                    : 'Try changing your search or filter criteria'
                  }
                </p>
              </div>
            )}
          </div>
        </section>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
