import { useState, useEffect } from 'react';
import { SiteSettings } from '../types/admin';
import { getSiteSettings } from '../data/settings';

/**
 * Hook مخصص لاستخدام إعدادات الموقع
 * يقوم بجلب الإعدادات من التخزين المحلي ويعيد تحديثها عند تغييرها
 */
export const useSiteSettings = () => {
  const [settings, setSettings] = useState<SiteSettings | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadSettings = () => {
      try {
        const siteSettings = getSiteSettings();
        setSettings(siteSettings);
      } catch (error) {
        console.error('Error loading site settings:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();

    // الاستماع لتغييرات التخزين المحلي
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'siteSettings') {
        loadSettings();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return {
    settings,
    loading,
    // دالة لإعادة تحميل الإعدادات يدوياً
    reload: () => {
      const siteSettings = getSiteSettings();
      setSettings(siteSettings);
    }
  };
};

/**
 * Hook مبسط للحصول على إعدادات محددة
 */
export const useHeaderSettings = () => {
  const { settings, loading } = useSiteSettings();
  return {
    headerSettings: settings?.headerSettings,
    loading
  };
};

export const useFooterSettings = () => {
  const { settings, loading } = useSiteSettings();
  return {
    footerSettings: settings?.footerSettings,
    loading
  };
};

export const useSocialLinks = () => {
  const { settings, loading } = useSiteSettings();
  return {
    socialLinks: settings?.socialLinks,
    loading
  };
};

export const useContactInfo = () => {
  const { settings, loading } = useSiteSettings();
  return {
    contactInfo: {
      email: settings?.contactEmail,
      phone: settings?.phone,
      whatsapp: settings?.whatsappNumber,
      address: settings?.address,
      addressAr: settings?.addressAr,
      workingHours: settings?.workingHours,
      workingHoursAr: settings?.workingHoursAr
    },
    loading
  };
};

export const useCompanyInfo = () => {
  const { settings, loading } = useSiteSettings();
  return {
    companyInfo: {
      name: settings?.siteName,
      nameAr: settings?.siteNameAr,
      about: settings?.aboutText,
      aboutAr: settings?.aboutTextAr,
      email: settings?.contactEmail,
      phone: settings?.phone,
      whatsapp: settings?.whatsappNumber,
      address: settings?.address,
      addressAr: settings?.addressAr,
      workingHours: settings?.workingHours,
      workingHoursAr: settings?.workingHoursAr
    },
    loading
  };
};
