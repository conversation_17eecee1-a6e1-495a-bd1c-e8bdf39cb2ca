import { NextApiRequest, NextApiResponse } from 'next';
import { getCategories, addCategory } from '../../../utils/database';
import { Category } from '../../../types/admin';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        const categories = getCategories();
        res.status(200).json(categories);
        break;

      case 'POST':
        const newCategoryData = req.body as Omit<Category, 'id' | 'createdAt' | 'updatedAt'>;
        const newCategory = addCategory(newCategoryData);
        res.status(201).json(newCategory);
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
