{"version": 3, "sources": ["../../../src/server/app-render/server-inserted-html.tsx"], "sourcesContent": ["// Provider for the `useServerInsertedHTML` API to register callbacks to insert\n// elements into the HTML stream.\n\nimport React, { type JSX } from 'react'\nimport { ServerInsertedHTMLContext } from '../../shared/lib/server-inserted-html.shared-runtime'\n\nexport function createServerInsertedHTML() {\n  const serverInsertedHTMLCallbacks: (() => React.ReactNode)[] = []\n  const addInsertedHtml = (handler: () => React.ReactNode) => {\n    serverInsertedHTMLCallbacks.push(handler)\n  }\n\n  return {\n    ServerInsertedHTMLProvider({ children }: { children: JSX.Element }) {\n      return (\n        <ServerInsertedHTMLContext.Provider value={addInsertedHtml}>\n          {children}\n        </ServerInsertedHTMLContext.Provider>\n      )\n    },\n    renderServerInsertedHTML() {\n      return serverInsertedHTMLCallbacks.map((callback, index) => (\n        <React.Fragment key={'__next_server_inserted__' + index}>\n          {callback()}\n        </React.Fragment>\n      ))\n    },\n  }\n}\n"], "names": ["React", "ServerInsertedHTMLContext", "createServerInsertedHTML", "serverInsertedHTMLCallbacks", "addInsertedHtml", "handler", "push", "ServerInsertedHTMLProvider", "children", "Provider", "value", "renderServerInsertedHTML", "map", "callback", "index", "Fragment"], "mappings": "AAAA,+EAA+E;AAC/E,iCAAiC;;AAEjC,OAAOA,WAAyB,QAAO;AACvC,SAASC,yBAAyB,QAAQ,uDAAsD;AAEhG,OAAO,SAASC;IACd,MAAMC,8BAAyD,EAAE;IACjE,MAAMC,kBAAkB,CAACC;QACvBF,4BAA4BG,IAAI,CAACD;IACnC;IAEA,OAAO;QACLE,4BAA2B,EAAEC,QAAQ,EAA6B;YAChE,qBACE,KAACP,0BAA0BQ,QAAQ;gBAACC,OAAON;0BACxCI;;QAGP;QACAG;YACE,OAAOR,4BAA4BS,GAAG,CAAC,CAACC,UAAUC,sBAChD,KAACd,MAAMe,QAAQ;8BACZF;mBADkB,6BAA6BC;QAItD;IACF;AACF"}