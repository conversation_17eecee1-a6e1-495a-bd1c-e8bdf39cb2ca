{"name": "react-server-dom-turbopack-builtin", "main": "index.js", "exports": {".": "./index.js", "./client": {"workerd": "./client.edge.js", "deno": "./client.edge.js", "worker": "./client.edge.js", "node": "./client.node.js", "edge-light": "./client.edge.js", "browser": "./client.browser.js", "default": "./client.browser.js"}, "./client.browser": "./client.browser.js", "./client.edge": "./client.edge.js", "./client.node": "./client.node.js", "./server": {"react-server": {"workerd": "./server.edge.js", "deno": "./server.browser.js", "node": "./server.node.js", "edge-light": "./server.edge.js", "browser": "./server.browser.js"}, "default": "./server.js"}, "./server.browser": "./server.browser.js", "./server.edge": "./server.edge.js", "./server.node": "./server.node.js", "./static": {"react-server": {"workerd": "./static.edge.js", "deno": "./static.browser.js", "node": "./static.node.js", "edge-light": "./static.edge.js", "browser": "./static.browser.js"}, "default": "./static.js"}, "./static.browser": "./static.browser.js", "./static.edge": "./static.edge.js", "./static.node": "./static.node.js", "./package.json": "./package.json"}, "dependencies": {"acorn-loose": "^8.3.0", "neo-async": "^2.6.1"}, "peerDependencies": {"react": "19.2.0-canary-3fbfb9ba-20250409", "react-dom": "19.2.0-canary-3fbfb9ba-20250409"}}