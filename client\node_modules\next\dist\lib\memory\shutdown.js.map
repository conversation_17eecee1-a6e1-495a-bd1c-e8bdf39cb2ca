{"version": 3, "sources": ["../../../src/lib/memory/shutdown.ts"], "sourcesContent": ["import { info } from '../../build/output/log'\nimport { bold } from '../picocolors'\nimport { getGcEvents, stopObservingGc } from './gc-observer'\nimport { getAllMemoryUsageSpans, stopPeriodicMemoryUsageTracing } from './trace'\n\nexport function disableMemoryDebuggingMode(): void {\n  stopPeriodicMemoryUsageTracing()\n  stopObservingGc()\n\n  info(bold('Memory usage report:'))\n\n  const gcEvents = getGcEvents()\n  const totalTimeInGcMs = gcEvents.reduce(\n    (acc, event) => acc + event.duration,\n    0\n  )\n  info(` - Total time spent in GC: ${totalTimeInGcMs.toFixed(2)}ms`)\n\n  const allMemoryUsage = getAllMemoryUsageSpans()\n  const peakHeapUsage = Math.max(\n    ...allMemoryUsage.map((usage) => usage['memory.heapUsed'])\n  )\n  const peakRssUsage = Math.max(\n    ...allMemoryUsage.map((usage) => usage['memory.rss'])\n  )\n  info(` - Peak heap usage: ${(peakHeapUsage / 1024 / 1024).toFixed(2)} MB`)\n  info(` - Peak RSS usage: ${(peakRssUsage / 1024 / 1024).toFixed(2)} MB`)\n}\n"], "names": ["disableMemoryDebuggingMode", "stopPeriodicMemoryUsageTracing", "stopObservingGc", "info", "bold", "gcEvents", "getGcEvents", "totalTimeInGcMs", "reduce", "acc", "event", "duration", "toFixed", "allMemoryUsage", "getAllMemoryUsageSpans", "peakHeapUsage", "Math", "max", "map", "usage", "peakRssUsage"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;qBALK;4BACA;4BACwB;uBAC0B;AAEhE,SAASA;IACdC,IAAAA,qCAA8B;IAC9BC,IAAAA,2BAAe;IAEfC,IAAAA,SAAI,EAACC,IAAAA,gBAAI,EAAC;IAEV,MAAMC,WAAWC,IAAAA,uBAAW;IAC5B,MAAMC,kBAAkBF,SAASG,MAAM,CACrC,CAACC,KAAKC,QAAUD,MAAMC,MAAMC,QAAQ,EACpC;IAEFR,IAAAA,SAAI,EAAC,CAAC,2BAA2B,EAAEI,gBAAgBK,OAAO,CAAC,GAAG,EAAE,CAAC;IAEjE,MAAMC,iBAAiBC,IAAAA,6BAAsB;IAC7C,MAAMC,gBAAgBC,KAAKC,GAAG,IACzBJ,eAAeK,GAAG,CAAC,CAACC,QAAUA,KAAK,CAAC,kBAAkB;IAE3D,MAAMC,eAAeJ,KAAKC,GAAG,IACxBJ,eAAeK,GAAG,CAAC,CAACC,QAAUA,KAAK,CAAC,aAAa;IAEtDhB,IAAAA,SAAI,EAAC,CAAC,oBAAoB,EAAE,AAACY,CAAAA,gBAAgB,OAAO,IAAG,EAAGH,OAAO,CAAC,GAAG,GAAG,CAAC;IACzET,IAAAA,SAAI,EAAC,CAAC,mBAAmB,EAAE,AAACiB,CAAAA,eAAe,OAAO,IAAG,EAAGR,OAAO,CAAC,GAAG,GAAG,CAAC;AACzE"}