{"version": 3, "sources": ["../../../src/lib/typescript/runTypeCheck.ts"], "sourcesContent": ["import path from 'path'\nimport {\n  DiagnosticCategory,\n  getFormattedDiagnostic,\n} from './diagnosticFormatter'\nimport { getTypeScriptConfiguration } from './getTypeScriptConfiguration'\nimport { getRequiredConfiguration } from './writeConfigurationDefaults'\n\nimport { CompileError } from '../compile-error'\nimport { warn } from '../../build/output/log'\n\nexport interface TypeCheckResult {\n  hasWarnings: boolean\n  warnings?: string[]\n  inputFilesCount: number\n  totalFilesCount: number\n  incremental: boolean\n}\n\nexport async function runTypeCheck(\n  ts: typeof import('typescript'),\n  baseDir: string,\n  distDir: string,\n  tsConfigPath: string,\n  cacheDir?: string,\n  isAppDirEnabled?: boolean\n): Promise<TypeCheckResult> {\n  const effectiveConfiguration = await getTypeScriptConfiguration(\n    ts,\n    tsConfigPath\n  )\n\n  if (effectiveConfiguration.fileNames.length < 1) {\n    return {\n      hasWarnings: false,\n      inputFilesCount: 0,\n      totalFilesCount: 0,\n      incremental: false,\n    }\n  }\n  const requiredConfig = getRequiredConfiguration(ts)\n\n  const options = {\n    ...requiredConfig,\n    ...effectiveConfiguration.options,\n    declarationMap: false,\n    emitDeclarationOnly: false,\n    noEmit: true,\n  }\n\n  let program:\n    | import('typescript').Program\n    | import('typescript').BuilderProgram\n  let incremental = false\n  if ((options.incremental || options.composite) && cacheDir) {\n    if (options.composite) {\n      warn(\n        'TypeScript project references are not fully supported. Attempting to build in incremental mode.'\n      )\n    }\n    incremental = true\n    program = ts.createIncrementalProgram({\n      rootNames: effectiveConfiguration.fileNames,\n      options: {\n        ...options,\n        composite: false,\n        incremental: true,\n        tsBuildInfoFile: path.join(cacheDir, '.tsbuildinfo'),\n      },\n    })\n  } else {\n    program = ts.createProgram(effectiveConfiguration.fileNames, options)\n  }\n\n  const result = program.emit()\n\n  const ignoreRegex = [\n    // matches **/__(tests|mocks)__/**\n    /[\\\\/]__(?:tests|mocks)__[\\\\/]/,\n    // matches **/*.(spec|test).*\n    /(?<=[\\\\/.])(?:spec|test)\\.[^\\\\/]+$/,\n  ]\n  const regexIgnoredFile = new RegExp(\n    ignoreRegex.map((r) => r.source).join('|')\n  )\n\n  const allDiagnostics = ts\n    .getPreEmitDiagnostics(program as import('typescript').Program)\n    .concat(result.diagnostics)\n    .filter((d) => !(d.file && regexIgnoredFile.test(d.file.fileName)))\n\n  const firstError =\n    allDiagnostics.find(\n      (d) => d.category === DiagnosticCategory.Error && Boolean(d.file)\n    ) ?? allDiagnostics.find((d) => d.category === DiagnosticCategory.Error)\n\n  // In test mode, we want to check all diagnostics, not just the first one.\n  if (process.env.__NEXT_TEST_MODE) {\n    if (firstError) {\n      const allErrors = allDiagnostics\n        .filter((d) => d.category === DiagnosticCategory.Error)\n        .map(\n          (d) =>\n            '[Test Mode] ' +\n            getFormattedDiagnostic(ts, baseDir, distDir, d, isAppDirEnabled)\n        )\n\n      console.error(\n        '\\n\\n===== TS errors =====\\n\\n' +\n          allErrors.join('\\n\\n') +\n          '\\n\\n===== TS errors =====\\n\\n'\n      )\n\n      // Make sure all stdout is flushed before we exit.\n      await new Promise((resolve) => setTimeout(resolve, 100))\n    }\n  }\n\n  if (firstError) {\n    throw new CompileError(\n      getFormattedDiagnostic(ts, baseDir, distDir, firstError, isAppDirEnabled)\n    )\n  }\n\n  const warnings = allDiagnostics\n    .filter((d) => d.category === DiagnosticCategory.Warning)\n    .map((d) =>\n      getFormattedDiagnostic(ts, baseDir, distDir, d, isAppDirEnabled)\n    )\n\n  return {\n    hasWarnings: true,\n    warnings,\n    inputFilesCount: effectiveConfiguration.fileNames.length,\n    totalFilesCount: program.getSourceFiles().length,\n    incremental,\n  }\n}\n"], "names": ["runTypeCheck", "ts", "baseDir", "distDir", "tsConfigPath", "cacheDir", "isAppDirEnabled", "effectiveConfiguration", "getTypeScriptConfiguration", "fileNames", "length", "hasWarnings", "inputFilesCount", "totalFilesCount", "incremental", "requiredConfig", "getRequiredConfiguration", "options", "declarationMap", "emitDeclarationOnly", "noEmit", "program", "composite", "warn", "createIncrementalProgram", "rootNames", "tsBuildInfoFile", "path", "join", "createProgram", "result", "emit", "ignoreRegex", "regexIgnoredFile", "RegExp", "map", "r", "source", "allDiagnostics", "getPreEmitDiagnostics", "concat", "diagnostics", "filter", "d", "file", "test", "fileName", "firstError", "find", "category", "DiagnosticCategory", "Error", "Boolean", "process", "env", "__NEXT_TEST_MODE", "allErrors", "getFormattedDiagnostic", "console", "error", "Promise", "resolve", "setTimeout", "CompileError", "warnings", "Warning", "getSourceFiles"], "mappings": ";;;;+BAmBsBA;;;eAAAA;;;6DAnBL;qCAIV;4CACoC;4CACF;8BAEZ;qBACR;;;;;;AAUd,eAAeA,aACpBC,EAA+B,EAC/BC,OAAe,EACfC,OAAe,EACfC,YAAoB,EACpBC,QAAiB,EACjBC,eAAyB;IAEzB,MAAMC,yBAAyB,MAAMC,IAAAA,sDAA0B,EAC7DP,IACAG;IAGF,IAAIG,uBAAuBE,SAAS,CAACC,MAAM,GAAG,GAAG;QAC/C,OAAO;YACLC,aAAa;YACbC,iBAAiB;YACjBC,iBAAiB;YACjBC,aAAa;QACf;IACF;IACA,MAAMC,iBAAiBC,IAAAA,oDAAwB,EAACf;IAEhD,MAAMgB,UAAU;QACd,GAAGF,cAAc;QACjB,GAAGR,uBAAuBU,OAAO;QACjCC,gBAAgB;QAChBC,qBAAqB;QACrBC,QAAQ;IACV;IAEA,IAAIC;IAGJ,IAAIP,cAAc;IAClB,IAAI,AAACG,CAAAA,QAAQH,WAAW,IAAIG,QAAQK,SAAS,AAAD,KAAMjB,UAAU;QAC1D,IAAIY,QAAQK,SAAS,EAAE;YACrBC,IAAAA,SAAI,EACF;QAEJ;QACAT,cAAc;QACdO,UAAUpB,GAAGuB,wBAAwB,CAAC;YACpCC,WAAWlB,uBAAuBE,SAAS;YAC3CQ,SAAS;gBACP,GAAGA,OAAO;gBACVK,WAAW;gBACXR,aAAa;gBACbY,iBAAiBC,aAAI,CAACC,IAAI,CAACvB,UAAU;YACvC;QACF;IACF,OAAO;QACLgB,UAAUpB,GAAG4B,aAAa,CAACtB,uBAAuBE,SAAS,EAAEQ;IAC/D;IAEA,MAAMa,SAAST,QAAQU,IAAI;IAE3B,MAAMC,cAAc;QAClB,kCAAkC;QAClC;QACA,6BAA6B;QAC7B;KACD;IACD,MAAMC,mBAAmB,IAAIC,OAC3BF,YAAYG,GAAG,CAAC,CAACC,IAAMA,EAAEC,MAAM,EAAET,IAAI,CAAC;IAGxC,MAAMU,iBAAiBrC,GACpBsC,qBAAqB,CAAClB,SACtBmB,MAAM,CAACV,OAAOW,WAAW,EACzBC,MAAM,CAAC,CAACC,IAAM,CAAEA,CAAAA,EAAEC,IAAI,IAAIX,iBAAiBY,IAAI,CAACF,EAAEC,IAAI,CAACE,QAAQ,CAAA;IAElE,MAAMC,aACJT,eAAeU,IAAI,CACjB,CAACL,IAAMA,EAAEM,QAAQ,KAAKC,uCAAkB,CAACC,KAAK,IAAIC,QAAQT,EAAEC,IAAI,MAC7DN,eAAeU,IAAI,CAAC,CAACL,IAAMA,EAAEM,QAAQ,KAAKC,uCAAkB,CAACC,KAAK;IAEzE,0EAA0E;IAC1E,IAAIE,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QAChC,IAAIR,YAAY;YACd,MAAMS,YAAYlB,eACfI,MAAM,CAAC,CAACC,IAAMA,EAAEM,QAAQ,KAAKC,uCAAkB,CAACC,KAAK,EACrDhB,GAAG,CACF,CAACQ,IACC,iBACAc,IAAAA,2CAAsB,EAACxD,IAAIC,SAASC,SAASwC,GAAGrC;YAGtDoD,QAAQC,KAAK,CACX,kCACEH,UAAU5B,IAAI,CAAC,UACf;YAGJ,kDAAkD;YAClD,MAAM,IAAIgC,QAAQ,CAACC,UAAYC,WAAWD,SAAS;QACrD;IACF;IAEA,IAAId,YAAY;QACd,MAAM,qBAEL,CAFK,IAAIgB,0BAAY,CACpBN,IAAAA,2CAAsB,EAACxD,IAAIC,SAASC,SAAS4C,YAAYzC,mBADrD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAM0D,WAAW1B,eACdI,MAAM,CAAC,CAACC,IAAMA,EAAEM,QAAQ,KAAKC,uCAAkB,CAACe,OAAO,EACvD9B,GAAG,CAAC,CAACQ,IACJc,IAAAA,2CAAsB,EAACxD,IAAIC,SAASC,SAASwC,GAAGrC;IAGpD,OAAO;QACLK,aAAa;QACbqD;QACApD,iBAAiBL,uBAAuBE,SAAS,CAACC,MAAM;QACxDG,iBAAiBQ,QAAQ6C,cAAc,GAAGxD,MAAM;QAChDI;IACF;AACF"}