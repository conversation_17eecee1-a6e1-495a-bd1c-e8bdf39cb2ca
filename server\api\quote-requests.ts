import { NextApiRequest, NextApiResponse } from 'next';
import * as XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';
import nodemailer from 'nodemailer';

interface CartItem {
  id: string;
  title: string;
  titleAr: string;
  price: number;
  quantity: number;
  image: string;
}

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: CartItem[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}

// دالة إرسال الإيميل
async function sendEmailWithExcel(quoteRequest: QuoteRequest, excelFilePath: string) {
  try {
    // قراءة إعدادات التواصل من الإعدادات المحفوظة
    const settingsPath = path.join(process.cwd(), 'src', 'data', 'site-settings.json');
    let emailSettings = {
      smtpHost: 'smtp.gmail.com',
      smtpPort: 587,
      smtpSecure: false,
      smtpUser: process.env.EMAIL_USER || '',
      smtpPass: process.env.EMAIL_PASS || '',
      adminEmail: process.env.ADMIN_EMAIL || '<EMAIL>',
      fromName: 'Droob Hajer',
      fromNameAr: 'دروب هاجر',
      enabled: true
    };

    // محاولة قراءة الإعدادات من الملف
    if (fs.existsSync(settingsPath)) {
      try {
        const settingsContent = fs.readFileSync(settingsPath, 'utf-8');
        const siteSettings = JSON.parse(settingsContent);
        if (siteSettings.communicationSettings?.email) {
          emailSettings = { ...emailSettings, ...siteSettings.communicationSettings.email };
        } else if (siteSettings.emailSettings) {
          // للتوافق مع النسخة القديمة
          emailSettings = { ...emailSettings, ...siteSettings.emailSettings };
        }
      } catch (error) {
        console.log('Using default email settings');
      }
    }

    // التحقق من تفعيل إرسال الإيميل
    if (!emailSettings.enabled || !emailSettings.smtpUser || !emailSettings.smtpPass) {
      console.log('Email sending is disabled or not configured');
      return { success: false, error: 'Email not configured' };
    }

    // إعدادات SMTP
    const transporter = nodemailer.createTransport({
      host: emailSettings.smtpHost,
      port: emailSettings.smtpPort,
      secure: emailSettings.smtpSecure,
      auth: {
        user: emailSettings.smtpUser,
        pass: emailSettings.smtpPass
      }
    });

    // قراءة ملف الإكسل
    const excelBuffer = fs.readFileSync(excelFilePath);

    // إعداد الإيميل
    const mailOptions = {
      from: `"${emailSettings.fromNameAr}" <${emailSettings.smtpUser}>`,
      to: emailSettings.adminEmail,
      subject: `طلب تسعير جديد - ${quoteRequest.id}`,
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif;">
          <h2>طلب تسعير جديد</h2>
          <p><strong>رقم الطلب:</strong> ${quoteRequest.id}</p>
          <p><strong>تاريخ الطلب:</strong> ${new Date(quoteRequest.createdAt).toLocaleDateString('ar-SA')}</p>

          <h3>بيانات العميل:</h3>
          <ul>
            <li><strong>الاسم:</strong> ${quoteRequest.customerInfo.name}</li>
            <li><strong>البريد الإلكتروني:</strong> ${quoteRequest.customerInfo.email}</li>
            <li><strong>رقم الهاتف:</strong> ${quoteRequest.customerInfo.phone}</li>
            <li><strong>الشركة:</strong> ${quoteRequest.customerInfo.company}</li>
          </ul>

          <h3>المنتجات المطلوبة:</h3>
          <ul>
            ${quoteRequest.products.map(product =>
              `<li>${product.titleAr || product.title} - الكمية: ${product.quantity}</li>`
            ).join('')}
          </ul>

          <p><strong>إجمالي عدد المنتجات:</strong> ${quoteRequest.products.length}</p>
          <p><strong>إجمالي الكمية:</strong> ${quoteRequest.products.reduce((total, item) => total + item.quantity, 0)}</p>

          <p>يرجى مراجعة ملف الإكسل المرفق لتفاصيل أكثر.</p>
        </div>
      `,
      attachments: [
        {
          filename: `quote-request-${quoteRequest.id}.xlsx`,
          content: excelBuffer,
          contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      ]
    };

    // إرسال الإيميل
    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      const { customerInfo, products } = req.body;

      // إنشاء معرف فريد للطلب
      const requestId = `QR-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      
      // حساب المجموع الكلي
      const totalAmount = products.reduce((total: number, item: CartItem) => 
        total + (item.price * item.quantity), 0
      );

      // إنشاء مجلد البيانات إذا لم يكن موجوداً
      const dataDir = path.join(process.cwd(), 'src', 'data');
      const quotesDir = path.join(dataDir, 'quotes');
      const excelDir = path.join(dataDir, 'excel');
      
      if (!fs.existsSync(quotesDir)) {
        fs.mkdirSync(quotesDir, { recursive: true });
      }
      if (!fs.existsSync(excelDir)) {
        fs.mkdirSync(excelDir, { recursive: true });
      }

      // إنشاء ملف Excel للمنتجات
      const excelFileName = `${requestId}.xlsx`;
      const excelFilePath = path.join(excelDir, excelFileName);
      
      // تحضير البيانات لملف Excel
      const excelData = products.map((product: CartItem, index: number) => ({
        'رقم المنتج': index + 1,
        'اسم المنتج': product.titleAr || product.title,
        'الكمية': product.quantity,
        'السعر المقترح': '', // سيتم ملؤه من قبل الإدارة
        'ملاحظات': ''
      }));

      // إنشاء workbook وworksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      
      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 10 }, // رقم المنتج
        { wch: 30 }, // اسم المنتج
        { wch: 10 }, // الكمية
        { wch: 15 }, // السعر المقترح
        { wch: 20 }  // ملاحظات
      ];
      worksheet['!cols'] = columnWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, 'طلب التسعير');
      XLSX.writeFile(workbook, excelFilePath);

      // إنشاء كائن طلب التسعير
      const quoteRequest: QuoteRequest = {
        id: requestId,
        customerInfo,
        products,
        totalAmount,
        createdAt: new Date().toISOString(),
        status: 'pending',
        excelFilePath: `src/data/excel/${excelFileName}`
      };

      // حفظ بيانات الطلب في ملف JSON
      const jsonFilePath = path.join(quotesDir, `${requestId}.json`);
      fs.writeFileSync(jsonFilePath, JSON.stringify(quoteRequest, null, 2));

      // تحديث ملف الفهرس العام للطلبات
      const indexFilePath = path.join(quotesDir, 'index.json');
      let allRequests: QuoteRequest[] = [];

      if (fs.existsSync(indexFilePath)) {
        const indexContent = fs.readFileSync(indexFilePath, 'utf-8');
        allRequests = JSON.parse(indexContent);
      }

      allRequests.unshift(quoteRequest); // إضافة الطلب الجديد في المقدمة
      fs.writeFileSync(indexFilePath, JSON.stringify(allRequests, null, 2));

      // إرسال الإيميل مع ملف الإكسل
      const emailResult = await sendEmailWithExcel(quoteRequest, excelFilePath);

      if (emailResult.success) {
        console.log('Email sent successfully for quote request:', requestId);
      } else {
        console.error('Failed to send email for quote request:', requestId, emailResult.error);
      }

      res.status(200).json({
        success: true,
        message: 'تم إرسال طلب التسعير بنجاح',
        requestId,
        excelFilePath: quoteRequest.excelFilePath,
        emailSent: emailResult.success
      });

    } catch (error) {
      console.error('Error creating quote request:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء معالجة الطلب'
      });
    }
  } else if (req.method === 'GET') {
    try {
      const quotesDir = path.join(process.cwd(), 'src', 'data', 'quotes');
      const indexFilePath = path.join(quotesDir, 'index.json');
      
      if (!fs.existsSync(indexFilePath)) {
        return res.status(200).json({ success: true, requests: [] });
      }
      
      const indexContent = fs.readFileSync(indexFilePath, 'utf-8');
      const allRequests = JSON.parse(indexContent);
      
      res.status(200).json({ success: true, requests: allRequests });
      
    } catch (error) {
      console.error('Error fetching quote requests:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء جلب الطلبات'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
