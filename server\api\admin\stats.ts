import { NextApiRequest, NextApiResponse } from 'next';
import { getStats } from '../../../utils/database';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        const stats = getStats();
        res.status(200).json({ success: true, data: stats });
        break;

      default:
        res.setHeader('Allow', ['GET']);
        res.status(405).json({ 
          success: false, 
          message: `Method ${req.method} not allowed` 
        });
    }
  } catch (error) {
    console.error('Stats API error:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
}
