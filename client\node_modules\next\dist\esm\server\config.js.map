{"version": 3, "sources": ["../../src/server/config.ts"], "sourcesContent": ["import { existsSync } from 'fs'\nimport { basename, extname, join, relative, isAbsolute, resolve } from 'path'\nimport { pathToFileURL } from 'url'\nimport findUp from 'next/dist/compiled/find-up'\nimport * as Log from '../build/output/log'\nimport { CONFIG_FILES, PHASE_DEVELOPMENT_SERVER } from '../shared/lib/constants'\nimport { defaultConfig, normalizeConfig } from './config-shared'\nimport type {\n  ExperimentalConfig,\n  NextConfigComplete,\n  NextConfig,\n  TurbopackLoaderItem,\n} from './config-shared'\n\nimport { loadWebpackHook } from './config-utils'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport type { ImageConfig } from '../shared/lib/image-config'\nimport { loadEnvConfig, updateInitialEnv } from '@next/env'\nimport { flushAndExit } from '../telemetry/flush-and-exit'\nimport { findRootDir } from '../lib/find-root'\nimport { setHttpClientAndAgentOptions } from './setup-http-agent-env'\nimport { pathHasPrefix } from '../shared/lib/router/utils/path-has-prefix'\nimport { matchRemotePattern } from '../shared/lib/match-remote-pattern'\n\nimport type { ZodError } from 'next/dist/compiled/zod'\nimport { hasNextSupport } from '../server/ci-info'\nimport { transpileConfig } from '../build/next-config-ts/transpile-config'\nimport { dset } from '../shared/lib/dset'\nimport { normalizeZodErrors } from '../shared/lib/zod'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport { findDir } from '../lib/find-pages-dir'\nimport { CanaryOnlyError, isStableBuild } from '../shared/lib/canary-only'\n\nexport { normalizeConfig } from './config-shared'\nexport type { DomainLocale, NextConfig } from './config-shared'\n\nfunction normalizeNextConfigZodErrors(\n  error: ZodError<NextConfig>\n): [errorMessages: string[], shouldExit: boolean] {\n  let shouldExit = false\n  const issues = normalizeZodErrors(error)\n  return [\n    issues.flatMap(({ issue, message }) => {\n      if (issue.path[0] === 'images') {\n        // We exit the build when encountering an error in the images config\n        shouldExit = true\n      }\n\n      return message\n    }),\n    shouldExit,\n  ]\n}\n\nexport function warnOptionHasBeenDeprecated(\n  config: NextConfig,\n  nestedPropertyKey: string,\n  reason: string,\n  silent: boolean\n): boolean {\n  let hasWarned = false\n  if (!silent) {\n    let current = config\n    let found = true\n    const nestedPropertyKeys = nestedPropertyKey.split('.')\n    for (const key of nestedPropertyKeys) {\n      if (current[key] !== undefined) {\n        current = current[key]\n      } else {\n        found = false\n        break\n      }\n    }\n    if (found) {\n      Log.warnOnce(reason)\n      hasWarned = true\n    }\n  }\n  return hasWarned\n}\n\nexport function warnOptionHasBeenMovedOutOfExperimental(\n  config: NextConfig,\n  oldExperimentalKey: string,\n  newKey: string,\n  configFileName: string,\n  silent: boolean\n) {\n  if (config.experimental && oldExperimentalKey in config.experimental) {\n    if (!silent) {\n      Log.warn(\n        `\\`experimental.${oldExperimentalKey}\\` has been moved to \\`${newKey}\\`. ` +\n          `Please update your ${configFileName} file accordingly.`\n      )\n    }\n\n    let current = config\n    const newKeys = newKey.split('.')\n    while (newKeys.length > 1) {\n      const key = newKeys.shift()!\n      current[key] = current[key] || {}\n      current = current[key]\n    }\n    current[newKeys.shift()!] = (config.experimental as any)[oldExperimentalKey]\n  }\n\n  return config\n}\n\nfunction warnCustomizedOption(\n  config: NextConfig,\n  key: string,\n  defaultValue: any,\n  customMessage: string,\n  configFileName: string,\n  silent: boolean\n) {\n  const segs = key.split('.')\n  let current = config\n\n  while (segs.length >= 1) {\n    const seg = segs.shift()!\n    if (!(seg in current)) {\n      return\n    }\n    current = current[seg]\n  }\n\n  if (!silent && current !== defaultValue) {\n    Log.warn(\n      `The \"${key}\" option has been modified. ${customMessage ? customMessage + '. ' : ''}It should be removed from your ${configFileName}.`\n    )\n  }\n}\n\nfunction assignDefaults(\n  dir: string,\n  userConfig: { [key: string]: any },\n  silent: boolean\n) {\n  const configFileName = userConfig.configFileName\n  if (typeof userConfig.exportTrailingSlash !== 'undefined') {\n    if (!silent) {\n      Log.warn(\n        `The \"exportTrailingSlash\" option has been renamed to \"trailingSlash\". Please update your ${configFileName}.`\n      )\n    }\n    if (typeof userConfig.trailingSlash === 'undefined') {\n      userConfig.trailingSlash = userConfig.exportTrailingSlash\n    }\n    delete userConfig.exportTrailingSlash\n  }\n\n  const config = Object.keys(userConfig).reduce<{ [key: string]: any }>(\n    (currentConfig, key) => {\n      const value = userConfig[key]\n\n      if (value === undefined || value === null) {\n        return currentConfig\n      }\n\n      if (key === 'distDir') {\n        if (typeof value !== 'string') {\n          throw new Error(\n            `Specified distDir is not a string, found type \"${typeof value}\"`\n          )\n        }\n        const userDistDir = value.trim()\n\n        // don't allow public as the distDir as this is a reserved folder for\n        // public files\n        if (userDistDir === 'public') {\n          throw new Error(\n            `The 'public' directory is reserved in Next.js and can not be set as the 'distDir'. https://nextjs.org/docs/messages/can-not-output-to-public`\n          )\n        }\n        // make sure distDir isn't an empty string as it can result in the provided\n        // directory being deleted in development mode\n        if (userDistDir.length === 0) {\n          throw new Error(\n            `Invalid distDir provided, distDir can not be an empty string. Please remove this config or set it to undefined`\n          )\n        }\n      }\n\n      if (key === 'pageExtensions') {\n        if (!Array.isArray(value)) {\n          throw new Error(\n            `Specified pageExtensions is not an array of strings, found \"${value}\". Please update this config or remove it.`\n          )\n        }\n\n        if (!value.length) {\n          throw new Error(\n            `Specified pageExtensions is an empty array. Please update it with the relevant extensions or remove it.`\n          )\n        }\n\n        value.forEach((ext) => {\n          if (typeof ext !== 'string') {\n            throw new Error(\n              `Specified pageExtensions is not an array of strings, found \"${ext}\" of type \"${typeof ext}\". Please update this config or remove it.`\n            )\n          }\n        })\n      }\n\n      if (!!value && value.constructor === Object) {\n        currentConfig[key] = {\n          ...defaultConfig[key],\n          ...Object.keys(value).reduce<any>((c, k) => {\n            const v = value[k]\n            if (v !== undefined && v !== null) {\n              c[k] = v\n            }\n            return c\n          }, {}),\n        }\n      } else {\n        currentConfig[key] = value\n      }\n\n      return currentConfig\n    },\n    {}\n  )\n\n  // TODO: remove these once we've made PPR default\n  // If this was defaulted to true, it implies that the configuration was\n  // overridden for testing to be defaulted on.\n  if (defaultConfig.experimental?.ppr) {\n    Log.warn(\n      `\\`experimental.ppr\\` has been defaulted to \\`true\\` because \\`__NEXT_EXPERIMENTAL_PPR\\` was set to \\`true\\` during testing.`\n    )\n  }\n\n  const result = { ...defaultConfig, ...config }\n\n  if (\n    result.experimental?.allowDevelopmentBuild &&\n    process.env.NODE_ENV !== 'development'\n  ) {\n    throw new Error(\n      `The experimental.allowDevelopmentBuild option requires NODE_ENV to be explicitly set to 'development'.`\n    )\n  }\n\n  if (isStableBuild()) {\n    // Prevents usage of certain experimental features outside of canary\n    if (result.experimental?.ppr) {\n      throw new CanaryOnlyError({ feature: 'experimental.ppr' })\n    } else if (result.experimental?.dynamicIO) {\n      throw new CanaryOnlyError({ feature: 'experimental.dynamicIO' })\n    } else if (result.experimental?.turbopackPersistentCaching) {\n      throw new CanaryOnlyError({\n        feature: 'experimental.turbopackPersistentCaching',\n      })\n    } else if (result.experimental?.nodeMiddleware) {\n      throw new CanaryOnlyError({ feature: 'experimental.nodeMiddleware' })\n    }\n  }\n\n  if (result.output === 'export') {\n    if (result.i18n) {\n      throw new Error(\n        'Specified \"i18n\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-i18n'\n      )\n    }\n\n    if (!hasNextSupport) {\n      if (result.rewrites) {\n        Log.warn(\n          'Specified \"rewrites\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n      if (result.redirects) {\n        Log.warn(\n          'Specified \"redirects\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n      if (result.headers) {\n        Log.warn(\n          'Specified \"headers\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n    }\n  }\n\n  if (typeof result.assetPrefix !== 'string') {\n    throw new Error(\n      `Specified assetPrefix is not a string, found type \"${typeof result.assetPrefix}\" https://nextjs.org/docs/messages/invalid-assetprefix`\n    )\n  }\n\n  if (typeof result.basePath !== 'string') {\n    throw new Error(\n      `Specified basePath is not a string, found type \"${typeof result.basePath}\"`\n    )\n  }\n\n  if (result.basePath !== '') {\n    if (result.basePath === '/') {\n      throw new Error(\n        `Specified basePath /. basePath has to be either an empty string or a path prefix\"`\n      )\n    }\n\n    if (!result.basePath.startsWith('/')) {\n      throw new Error(\n        `Specified basePath has to start with a /, found \"${result.basePath}\"`\n      )\n    }\n\n    if (result.basePath !== '/') {\n      if (result.basePath.endsWith('/')) {\n        throw new Error(\n          `Specified basePath should not end with /, found \"${result.basePath}\"`\n        )\n      }\n\n      if (result.assetPrefix === '') {\n        result.assetPrefix = result.basePath\n      }\n\n      if (result.amp?.canonicalBase === '') {\n        result.amp.canonicalBase = result.basePath\n      }\n    }\n  }\n\n  if (result?.images) {\n    const images: ImageConfig = result.images\n\n    if (typeof images !== 'object') {\n      throw new Error(\n        `Specified images should be an object received ${typeof images}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n      )\n    }\n\n    if (images.localPatterns) {\n      if (!Array.isArray(images.localPatterns)) {\n        throw new Error(\n          `Specified images.localPatterns should be an Array received ${typeof images.localPatterns}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n      // avoid double-pushing the same pattern if it already exists\n      const hasMatch = images.localPatterns.some(\n        (pattern) =>\n          pattern.pathname === '/_next/static/media/**' && pattern.search === ''\n      )\n      if (!hasMatch) {\n        // static import images are automatically allowed\n        images.localPatterns.push({\n          pathname: '/_next/static/media/**',\n          search: '',\n        })\n      }\n    }\n\n    if (images.remotePatterns) {\n      if (!Array.isArray(images.remotePatterns)) {\n        throw new Error(\n          `Specified images.remotePatterns should be an Array received ${typeof images.remotePatterns}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n\n      // We must convert URL to RemotePattern since URL has a colon in the protocol\n      // and also has additional properties we want to filter out. Also, new URL()\n      // accepts any protocol so we need manual validation here.\n      images.remotePatterns = images.remotePatterns.map(\n        ({ protocol, hostname, port, pathname, search }) => {\n          const proto = protocol?.replace(/:$/, '')\n          if (!['http', 'https', undefined].includes(proto)) {\n            throw new Error(\n              `Specified images.remotePatterns must have protocol \"http\" or \"https\" received \"${proto}\".`\n            )\n          }\n          return {\n            protocol: proto as 'http' | 'https' | undefined,\n            hostname,\n            port,\n            pathname,\n            search,\n          }\n        }\n      )\n\n      // static images are automatically prefixed with assetPrefix\n      // so we need to ensure _next/image allows downloading from\n      // this resource\n      if (config.assetPrefix?.startsWith('http')) {\n        try {\n          const url = new URL(config.assetPrefix)\n          const hasMatchForAssetPrefix = images.remotePatterns.some((pattern) =>\n            matchRemotePattern(pattern, url)\n          )\n\n          // avoid double-pushing the same pattern if it already can be matched\n          if (!hasMatchForAssetPrefix) {\n            images.remotePatterns.push({\n              hostname: url.hostname,\n              protocol: url.protocol.replace(/:$/, '') as 'http' | 'https',\n              port: url.port,\n            })\n          }\n        } catch (error) {\n          throw new Error(\n            `Invalid assetPrefix provided. Original error: ${error}`\n          )\n        }\n      }\n    }\n\n    if (images.domains) {\n      if (!Array.isArray(images.domains)) {\n        throw new Error(\n          `Specified images.domains should be an Array received ${typeof images.domains}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n    }\n\n    if (!images.loader) {\n      images.loader = 'default'\n    }\n\n    if (\n      images.loader !== 'default' &&\n      images.loader !== 'custom' &&\n      images.path === imageConfigDefault.path\n    ) {\n      throw new Error(\n        `Specified images.loader property (${images.loader}) also requires images.path property to be assigned to a URL prefix.\\nSee more info here: https://nextjs.org/docs/api-reference/next/legacy/image#loader-configuration`\n      )\n    }\n\n    if (\n      images.path === imageConfigDefault.path &&\n      result.basePath &&\n      !pathHasPrefix(images.path, result.basePath)\n    ) {\n      images.path = `${result.basePath}${images.path}`\n    }\n\n    // Append trailing slash for non-default loaders and when trailingSlash is set\n    if (\n      images.path &&\n      !images.path.endsWith('/') &&\n      (images.loader !== 'default' || result.trailingSlash)\n    ) {\n      images.path += '/'\n    }\n\n    if (images.loaderFile) {\n      if (images.loader !== 'default' && images.loader !== 'custom') {\n        throw new Error(\n          `Specified images.loader property (${images.loader}) cannot be used with images.loaderFile property. Please set images.loader to \"custom\".`\n        )\n      }\n      const absolutePath = join(dir, images.loaderFile)\n      if (!existsSync(absolutePath)) {\n        throw new Error(\n          `Specified images.loaderFile does not exist at \"${absolutePath}\".`\n        )\n      }\n      images.loaderFile = absolutePath\n    }\n  }\n\n  warnCustomizedOption(\n    result,\n    'experimental.esmExternals',\n    true,\n    'experimental.esmExternals is not recommended to be modified as it may disrupt module resolution',\n    configFileName,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    result,\n    'experimental.instrumentationHook',\n    `\\`experimental.instrumentationHook\\` is no longer needed, because \\`instrumentation.js\\` is available by default. You can remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    result,\n    'experimental.after',\n    `\\`experimental.after\\` is no longer needed, because \\`after\\` is available by default. You can remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    result,\n    'devIndicators.appIsrStatus',\n    `\\`devIndicators.appIsrStatus\\` is deprecated and no longer configurable. Please remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    result,\n    'devIndicators.buildActivity',\n    `\\`devIndicators.buildActivity\\` is deprecated and no longer configurable. Please remove it from ${configFileName}.`,\n    silent\n  )\n\n  const hasWarnedBuildActivityPosition = warnOptionHasBeenDeprecated(\n    result,\n    'devIndicators.buildActivityPosition',\n    `\\`devIndicators.buildActivityPosition\\` has been renamed to \\`devIndicators.position\\`. Please update your ${configFileName} file accordingly.`,\n    silent\n  )\n  if (\n    hasWarnedBuildActivityPosition &&\n    result.devIndicators !== false &&\n    result.devIndicators?.buildActivityPosition &&\n    result.devIndicators.buildActivityPosition !== result.devIndicators.position\n  ) {\n    Log.warnOnce(\n      `The \\`devIndicators\\` option \\`buildActivityPosition\\` (\"${result.devIndicators.buildActivityPosition}\") conflicts with \\`position\\` (\"${result.devIndicators.position}\"). Using \\`buildActivityPosition\\` (\"${result.devIndicators.buildActivityPosition}\") for backward compatibility.`\n    )\n    result.devIndicators.position = result.devIndicators.buildActivityPosition\n  }\n\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'bundlePagesExternals',\n    'bundlePagesRouterDependencies',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'serverComponentsExternalPackages',\n    'serverExternalPackages',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'relay',\n    'compiler.relay',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'styledComponents',\n    'compiler.styledComponents',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'emotion',\n    'compiler.emotion',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'reactRemoveProperties',\n    'compiler.reactRemoveProperties',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'removeConsole',\n    'compiler.removeConsole',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'swrDelta',\n    'expireTime',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingRoot',\n    'outputFileTracingRoot',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingIncludes',\n    'outputFileTracingIncludes',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingExcludes',\n    'outputFileTracingExcludes',\n    configFileName,\n    silent\n  )\n\n  if ((result.experimental as any).outputStandalone) {\n    if (!silent) {\n      Log.warn(\n        `experimental.outputStandalone has been renamed to \"output: 'standalone'\", please move the config.`\n      )\n    }\n    result.output = 'standalone'\n  }\n\n  if (\n    typeof result.experimental?.serverActions?.bodySizeLimit !== 'undefined'\n  ) {\n    const value = parseInt(\n      result.experimental.serverActions?.bodySizeLimit.toString()\n    )\n    if (isNaN(value) || value < 1) {\n      throw new Error(\n        'Server Actions Size Limit must be a valid number or filesize format larger than 1MB: https://nextjs.org/docs/app/api-reference/next-config-js/serverActions#bodysizelimit'\n      )\n    }\n  }\n\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'transpilePackages',\n    'transpilePackages',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'skipMiddlewareUrlNormalize',\n    'skipMiddlewareUrlNormalize',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'skipTrailingSlashRedirect',\n    'skipTrailingSlashRedirect',\n    configFileName,\n    silent\n  )\n\n  if (\n    result?.outputFileTracingRoot &&\n    !isAbsolute(result.outputFileTracingRoot)\n  ) {\n    result.outputFileTracingRoot = resolve(result.outputFileTracingRoot)\n    if (!silent) {\n      Log.warn(\n        `outputFileTracingRoot should be absolute, using: ${result.outputFileTracingRoot}`\n      )\n    }\n  }\n\n  if (result?.turbopack?.root && !isAbsolute(result.turbopack.root)) {\n    result.turbopack.root = resolve(result.turbopack.root)\n    if (!silent) {\n      Log.warn(\n        `turbopack.root should be absolute, using: ${result.turbopack.root}`\n      )\n    }\n  }\n\n  // only leverage deploymentId\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    result.deploymentId = process.env.NEXT_DEPLOYMENT_ID\n  }\n\n  if (result?.outputFileTracingRoot && !result?.turbopack?.root) {\n    dset(result, ['turbopack', 'root'], result.outputFileTracingRoot)\n  }\n\n  // use the closest lockfile as tracing root\n  if (!result?.outputFileTracingRoot || !result?.turbopack?.root) {\n    let rootDir = findRootDir(dir)\n\n    if (rootDir) {\n      if (!result?.outputFileTracingRoot) {\n        result.outputFileTracingRoot = rootDir\n      }\n\n      if (!result?.turbopack?.root) {\n        dset(result, ['turbopack', 'root'], rootDir)\n      }\n    }\n  }\n\n  setHttpClientAndAgentOptions(result || defaultConfig)\n\n  if (result.i18n) {\n    const hasAppDir = Boolean(findDir(dir, 'app'))\n\n    if (hasAppDir) {\n      warnOptionHasBeenDeprecated(\n        result,\n        'i18n',\n        `i18n configuration in ${configFileName} is unsupported in App Router.\\nLearn more about internationalization in App Router: https://nextjs.org/docs/app/building-your-application/routing/internationalization`,\n        silent\n      )\n    }\n\n    const { i18n } = result\n    const i18nType = typeof i18n\n\n    if (i18nType !== 'object') {\n      throw new Error(\n        `Specified i18n should be an object received ${i18nType}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (!Array.isArray(i18n.locales)) {\n      throw new Error(\n        `Specified i18n.locales should be an Array received ${typeof i18n.locales}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (i18n.locales.length > 100 && !silent) {\n      Log.warn(\n        `Received ${i18n.locales.length} i18n.locales items which exceeds the recommended max of 100.\\nSee more info here: https://nextjs.org/docs/advanced-features/i18n-routing#how-does-this-work-with-static-generation`\n      )\n    }\n\n    const defaultLocaleType = typeof i18n.defaultLocale\n\n    if (!i18n.defaultLocale || defaultLocaleType !== 'string') {\n      throw new Error(\n        `Specified i18n.defaultLocale should be a string.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (typeof i18n.domains !== 'undefined' && !Array.isArray(i18n.domains)) {\n      throw new Error(\n        `Specified i18n.domains must be an array of domain objects e.g. [ { domain: 'example.fr', defaultLocale: 'fr', locales: ['fr'] } ] received ${typeof i18n.domains}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (i18n.domains) {\n      const invalidDomainItems = i18n.domains.filter((item) => {\n        if (!item || typeof item !== 'object') return true\n        if (!item.defaultLocale) return true\n        if (!item.domain || typeof item.domain !== 'string') return true\n\n        if (item.domain.includes(':')) {\n          console.warn(\n            `i18n domain: \"${item.domain}\" is invalid it should be a valid domain without protocol (https://) or port (:3000) e.g. example.vercel.sh`\n          )\n          return true\n        }\n\n        const defaultLocaleDuplicate = i18n.domains?.find(\n          (altItem) =>\n            altItem.defaultLocale === item.defaultLocale &&\n            altItem.domain !== item.domain\n        )\n\n        if (!silent && defaultLocaleDuplicate) {\n          console.warn(\n            `Both ${item.domain} and ${defaultLocaleDuplicate.domain} configured the defaultLocale ${item.defaultLocale} but only one can. Change one item's default locale to continue`\n          )\n          return true\n        }\n\n        let hasInvalidLocale = false\n\n        if (Array.isArray(item.locales)) {\n          for (const locale of item.locales) {\n            if (typeof locale !== 'string') hasInvalidLocale = true\n\n            for (const domainItem of i18n.domains || []) {\n              if (domainItem === item) continue\n              if (domainItem.locales && domainItem.locales.includes(locale)) {\n                console.warn(\n                  `Both ${item.domain} and ${domainItem.domain} configured the locale (${locale}) but only one can. Remove it from one i18n.domains config to continue`\n                )\n                hasInvalidLocale = true\n                break\n              }\n            }\n          }\n        }\n\n        return hasInvalidLocale\n      })\n\n      if (invalidDomainItems.length > 0) {\n        throw new Error(\n          `Invalid i18n.domains values:\\n${invalidDomainItems\n            .map((item: any) => JSON.stringify(item))\n            .join(\n              '\\n'\n            )}\\n\\ndomains value must follow format { domain: 'example.fr', defaultLocale: 'fr', locales: ['fr'] }.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n        )\n      }\n    }\n\n    if (!Array.isArray(i18n.locales)) {\n      throw new Error(\n        `Specified i18n.locales must be an array of locale strings e.g. [\"en-US\", \"nl-NL\"] received ${typeof i18n.locales}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    const invalidLocales = i18n.locales.filter(\n      (locale: any) => typeof locale !== 'string'\n    )\n\n    if (invalidLocales.length > 0) {\n      throw new Error(\n        `Specified i18n.locales contains invalid values (${invalidLocales\n          .map(String)\n          .join(\n            ', '\n          )}), locales must be valid locale tags provided as strings e.g. \"en-US\".\\n` +\n          `See here for list of valid language sub-tags: http://www.iana.org/assignments/language-subtag-registry/language-subtag-registry`\n      )\n    }\n\n    if (!i18n.locales.includes(i18n.defaultLocale)) {\n      throw new Error(\n        `Specified i18n.defaultLocale should be included in i18n.locales.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    const normalizedLocales = new Set()\n    const duplicateLocales = new Set()\n\n    i18n.locales.forEach((locale) => {\n      const localeLower = locale.toLowerCase()\n      if (normalizedLocales.has(localeLower)) {\n        duplicateLocales.add(locale)\n      }\n      normalizedLocales.add(localeLower)\n    })\n\n    if (duplicateLocales.size > 0) {\n      throw new Error(\n        `Specified i18n.locales contains the following duplicate locales:\\n` +\n          `${[...duplicateLocales].join(', ')}\\n` +\n          `Each locale should be listed only once.\\n` +\n          `See more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    // make sure default Locale is at the front\n    i18n.locales = [\n      i18n.defaultLocale,\n      ...i18n.locales.filter((locale) => locale !== i18n.defaultLocale),\n    ]\n\n    const localeDetectionType = typeof i18n.localeDetection\n\n    if (\n      localeDetectionType !== 'boolean' &&\n      localeDetectionType !== 'undefined'\n    ) {\n      throw new Error(\n        `Specified i18n.localeDetection should be undefined or a boolean received ${localeDetectionType}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n  }\n\n  if (result.devIndicators !== false && result.devIndicators?.position) {\n    const { position } = result.devIndicators\n    const allowedValues = [\n      'top-left',\n      'top-right',\n      'bottom-left',\n      'bottom-right',\n    ]\n\n    if (!allowedValues.includes(position)) {\n      throw new Error(\n        `Invalid \"devIndicator.position\" provided, expected one of ${allowedValues.join(\n          ', '\n        )}, received ${position}`\n      )\n    }\n  }\n\n  if (result.experimental) {\n    result.experimental.cacheLife = {\n      ...defaultConfig.experimental?.cacheLife,\n      ...result.experimental.cacheLife,\n    }\n    const defaultDefault = defaultConfig.experimental?.cacheLife?.['default']\n    if (\n      !defaultDefault ||\n      defaultDefault.revalidate === undefined ||\n      defaultDefault.expire === undefined ||\n      !defaultConfig.experimental?.staleTimes?.static\n    ) {\n      throw new Error('No default cacheLife profile.')\n    }\n    const defaultCacheLifeProfile = result.experimental.cacheLife['default']\n    if (!defaultCacheLifeProfile) {\n      result.experimental.cacheLife['default'] = defaultDefault\n    } else {\n      if (defaultCacheLifeProfile.stale === undefined) {\n        const staticStaleTime = result.experimental.staleTimes?.static\n        defaultCacheLifeProfile.stale =\n          staticStaleTime ?? defaultConfig.experimental?.staleTimes?.static\n      }\n      if (defaultCacheLifeProfile.revalidate === undefined) {\n        defaultCacheLifeProfile.revalidate = defaultDefault.revalidate\n      }\n      if (defaultCacheLifeProfile.expire === undefined) {\n        defaultCacheLifeProfile.expire =\n          result.expireTime ?? defaultDefault.expire\n      }\n    }\n    // This is the most dynamic cache life profile.\n    const secondsCacheLifeProfile = result.experimental.cacheLife['seconds']\n    if (\n      secondsCacheLifeProfile &&\n      secondsCacheLifeProfile.stale === undefined\n    ) {\n      // We default this to whatever stale time you had configured for dynamic content.\n      // Since this is basically a dynamic cache life profile.\n      const dynamicStaleTime = result.experimental.staleTimes?.dynamic\n      secondsCacheLifeProfile.stale =\n        dynamicStaleTime ?? defaultConfig.experimental?.staleTimes?.dynamic\n    }\n  }\n\n  if (result.experimental?.cacheHandlers) {\n    const allowedHandlerNameRegex = /[a-z-]/\n\n    if (typeof result.experimental.cacheHandlers !== 'object') {\n      throw new Error(\n        `Invalid \"experimental.cacheHandlers\" provided, expected an object e.g. { default: '/my-handler.js' }, received ${JSON.stringify(result.experimental.cacheHandlers)}`\n      )\n    }\n\n    const handlerKeys = Object.keys(result.experimental.cacheHandlers)\n    const invalidHandlerItems: Array<{ key: string; reason: string }> = []\n\n    for (const key of handlerKeys) {\n      if (!allowedHandlerNameRegex.test(key)) {\n        invalidHandlerItems.push({\n          key,\n          reason: 'key must only use characters a-z and -',\n        })\n      } else {\n        const handlerPath = result.experimental.cacheHandlers[key]\n\n        if (handlerPath && !existsSync(handlerPath)) {\n          invalidHandlerItems.push({\n            key,\n            reason: `cache handler path provided does not exist, received ${handlerPath}`,\n          })\n        }\n      }\n      if (invalidHandlerItems.length) {\n        throw new Error(\n          `Invalid handler fields configured for \"experimental.cacheHandler\":\\n${invalidHandlerItems.map((item) => `${key}: ${item.reason}`).join('\\n')}`\n        )\n      }\n    }\n  }\n\n  const userProvidedModularizeImports = result.modularizeImports\n  // Unfortunately these packages end up re-exporting 10600 modules, for example: https://unpkg.com/browse/@mui/icons-material@5.11.16/esm/index.js.\n  // Leveraging modularizeImports tremendously reduces compile times for these.\n  result.modularizeImports = {\n    ...(userProvidedModularizeImports || {}),\n    // This is intentionally added after the user-provided modularizeImports config.\n    '@mui/icons-material': {\n      transform: '@mui/icons-material/{{member}}',\n    },\n    lodash: {\n      transform: 'lodash/{{member}}',\n    },\n  }\n\n  const userProvidedOptimizePackageImports =\n    result.experimental?.optimizePackageImports || []\n  if (!result.experimental) {\n    result.experimental = {}\n  }\n\n  result.experimental.optimizePackageImports = [\n    ...new Set([\n      ...userProvidedOptimizePackageImports,\n      'lucide-react',\n      'date-fns',\n      'lodash-es',\n      'ramda',\n      'antd',\n      'react-bootstrap',\n      'ahooks',\n      '@ant-design/icons',\n      '@headlessui/react',\n      '@headlessui-float/react',\n      '@heroicons/react/20/solid',\n      '@heroicons/react/24/solid',\n      '@heroicons/react/24/outline',\n      '@visx/visx',\n      '@tremor/react',\n      'rxjs',\n      '@mui/material',\n      '@mui/icons-material',\n      'recharts',\n      'react-use',\n      'effect',\n      '@effect/schema',\n      '@effect/platform',\n      '@effect/platform-node',\n      '@effect/platform-browser',\n      '@effect/platform-bun',\n      '@effect/sql',\n      '@effect/sql-mssql',\n      '@effect/sql-mysql2',\n      '@effect/sql-pg',\n      '@effect/sql-squlite-node',\n      '@effect/sql-squlite-bun',\n      '@effect/sql-squlite-wasm',\n      '@effect/sql-squlite-react-native',\n      '@effect/sql-squlite-wasm',\n      '@effect/rpc',\n      '@effect/rpc-http',\n      '@effect/typeclass',\n      '@effect/experimental',\n      '@effect/opentelemetry',\n      '@material-ui/core',\n      '@material-ui/icons',\n      '@tabler/icons-react',\n      'mui-core',\n      // We don't support wildcard imports for these configs, e.g. `react-icons/*`\n      // so we need to add them manually.\n      // In the future, we should consider automatically detecting packages that\n      // need to be optimized.\n      'react-icons/ai',\n      'react-icons/bi',\n      'react-icons/bs',\n      'react-icons/cg',\n      'react-icons/ci',\n      'react-icons/di',\n      'react-icons/fa',\n      'react-icons/fa6',\n      'react-icons/fc',\n      'react-icons/fi',\n      'react-icons/gi',\n      'react-icons/go',\n      'react-icons/gr',\n      'react-icons/hi',\n      'react-icons/hi2',\n      'react-icons/im',\n      'react-icons/io',\n      'react-icons/io5',\n      'react-icons/lia',\n      'react-icons/lib',\n      'react-icons/lu',\n      'react-icons/md',\n      'react-icons/pi',\n      'react-icons/ri',\n      'react-icons/rx',\n      'react-icons/si',\n      'react-icons/sl',\n      'react-icons/tb',\n      'react-icons/tfi',\n      'react-icons/ti',\n      'react-icons/vsc',\n      'react-icons/wi',\n    ]),\n  ]\n\n  if (!result.htmlLimitedBots) {\n    // @ts-expect-error: override the htmlLimitedBots with default string, type covert: RegExp -> string\n    result.htmlLimitedBots = HTML_LIMITED_BOT_UA_RE_STRING\n  }\n\n  // \"use cache\" was originally implicitly enabled with the dynamicIO flag, so\n  // we transfer the value for dynamicIO to the explicit useCache flag to ensure\n  // backwards compatibility.\n  if (result.experimental.useCache === undefined) {\n    result.experimental.useCache = result.experimental.dynamicIO\n  }\n\n  return result\n}\n\nexport default async function loadConfig(\n  phase: string,\n  dir: string,\n  {\n    customConfig,\n    rawConfig,\n    silent = true,\n    onLoadUserConfig,\n    reactProductionProfiling,\n  }: {\n    customConfig?: object | null\n    rawConfig?: boolean\n    silent?: boolean\n    onLoadUserConfig?: (conf: NextConfig) => void\n    reactProductionProfiling?: boolean\n  } = {}\n): Promise<NextConfigComplete> {\n  if (!process.env.__NEXT_PRIVATE_RENDER_WORKER) {\n    try {\n      loadWebpackHook()\n    } catch (err) {\n      // this can fail in standalone mode as the files\n      // aren't traced/included\n      if (!process.env.__NEXT_PRIVATE_STANDALONE_CONFIG) {\n        throw err\n      }\n    }\n  }\n\n  if (process.env.__NEXT_PRIVATE_STANDALONE_CONFIG) {\n    return JSON.parse(process.env.__NEXT_PRIVATE_STANDALONE_CONFIG)\n  }\n\n  const curLog = silent\n    ? {\n        warn: () => {},\n        info: () => {},\n        error: () => {},\n      }\n    : Log\n\n  loadEnvConfig(dir, phase === PHASE_DEVELOPMENT_SERVER, curLog)\n\n  let configFileName = 'next.config.js'\n\n  if (customConfig) {\n    return assignDefaults(\n      dir,\n      {\n        configOrigin: 'server',\n        configFileName,\n        ...customConfig,\n      },\n      silent\n    ) as NextConfigComplete\n  }\n\n  const path = await findUp(CONFIG_FILES, { cwd: dir })\n\n  if (process.env.__NEXT_TEST_MODE) {\n    if (path) {\n      Log.info(`Loading config from ${path}`)\n    } else {\n      Log.info('No config file found')\n    }\n  }\n\n  // If config file was found\n  if (path?.length) {\n    configFileName = basename(path)\n\n    let userConfigModule: any\n    try {\n      const envBefore = Object.assign({}, process.env)\n\n      // `import()` expects url-encoded strings, so the path must be properly\n      // escaped and (especially on Windows) absolute paths must pe prefixed\n      // with the `file://` protocol\n      if (process.env.__NEXT_TEST_MODE === 'jest') {\n        // dynamic import does not currently work inside of vm which\n        // jest relies on so we fall back to require for this case\n        // https://github.com/nodejs/node/issues/35889\n        userConfigModule = require(path)\n      } else if (configFileName === 'next.config.ts') {\n        userConfigModule = await transpileConfig({\n          nextConfigPath: path,\n          cwd: dir,\n        })\n      } else {\n        userConfigModule = await import(pathToFileURL(path).href)\n      }\n      const newEnv: typeof process.env = {} as any\n\n      for (const key of Object.keys(process.env)) {\n        if (envBefore[key] !== process.env[key]) {\n          newEnv[key] = process.env[key]\n        }\n      }\n      updateInitialEnv(newEnv)\n\n      if (rawConfig) {\n        return userConfigModule\n      }\n    } catch (err) {\n      // TODO: Modify docs to add cases of failing next.config.ts transformation\n      curLog.error(\n        `Failed to load ${configFileName}, see more info here https://nextjs.org/docs/messages/next-config-error`\n      )\n      throw err\n    }\n\n    const userConfig = (await normalizeConfig(\n      phase,\n      userConfigModule.default || userConfigModule\n    )) as NextConfig\n\n    if (!process.env.NEXT_MINIMAL) {\n      // We only validate the config against schema in non minimal mode\n      const { configSchema } =\n        require('./config-schema') as typeof import('./config-schema')\n      const state = configSchema.safeParse(userConfig)\n\n      if (state.success === false) {\n        // error message header\n        const messages = [`Invalid ${configFileName} options detected: `]\n\n        const [errorMessages, shouldExit] = normalizeNextConfigZodErrors(\n          state.error\n        )\n        // ident list item\n        for (const error of errorMessages) {\n          messages.push(`    ${error}`)\n        }\n\n        // error message footer\n        messages.push(\n          'See more info here: https://nextjs.org/docs/messages/invalid-next-config'\n        )\n\n        if (shouldExit) {\n          for (const message of messages) {\n            console.error(message)\n          }\n          await flushAndExit(1)\n        } else {\n          for (const message of messages) {\n            curLog.warn(message)\n          }\n        }\n      }\n    }\n\n    if (userConfig.target && userConfig.target !== 'server') {\n      throw new Error(\n        `The \"target\" property is no longer supported in ${configFileName}.\\n` +\n          'See more info here https://nextjs.org/docs/messages/deprecated-target-config'\n      )\n    }\n\n    if (userConfig.amp?.canonicalBase) {\n      const { canonicalBase } = userConfig.amp || ({} as any)\n      userConfig.amp = userConfig.amp || {}\n      userConfig.amp.canonicalBase =\n        (canonicalBase?.endsWith('/')\n          ? canonicalBase.slice(0, -1)\n          : canonicalBase) || ''\n    }\n\n    if (reactProductionProfiling) {\n      userConfig.reactProductionProfiling = reactProductionProfiling\n    }\n\n    if (\n      userConfig.experimental?.turbo?.loaders &&\n      !userConfig.experimental?.turbo?.rules\n    ) {\n      curLog.warn(\n        'experimental.turbo.loaders is now deprecated. Please update next.config.js to use experimental.turbo.rules as soon as possible.\\n' +\n          'The new option is similar, but the key should be a glob instead of an extension.\\n' +\n          'Example: loaders: { \".mdx\": [\"mdx-loader\"] } -> rules: { \"*.mdx\": [\"mdx-loader\"] }\" }\\n' +\n          'See more info here https://nextjs.org/docs/app/api-reference/next-config-js/turbo'\n      )\n\n      const rules: Record<string, TurbopackLoaderItem[]> = {}\n      for (const [ext, loaders] of Object.entries(\n        userConfig.experimental.turbo.loaders\n      )) {\n        rules['*' + ext] = loaders as TurbopackLoaderItem[]\n      }\n\n      userConfig.experimental.turbo.rules = rules\n    }\n\n    if (userConfig.experimental?.turbo) {\n      curLog.warn(\n        'The config property `experimental.turbo` is deprecated. Move this setting to `config.turbopack` as Turbopack is now stable.'\n      )\n\n      // Merge the two configs, preferring values in `config.turbopack`.\n      userConfig.turbopack = {\n        ...userConfig.experimental.turbo,\n        ...userConfig.turbopack,\n      }\n      userConfig.experimental.turbopackMemoryLimit ??=\n        userConfig.experimental.turbo.memoryLimit\n      userConfig.experimental.turbopackMinify ??=\n        userConfig.experimental.turbo.minify\n      userConfig.experimental.turbopackTreeShaking ??=\n        userConfig.experimental.turbo.treeShaking\n      userConfig.experimental.turbopackSourceMaps ??=\n        userConfig.experimental.turbo.sourceMaps\n    }\n\n    if (userConfig.experimental?.useLightningcss) {\n      const { loadBindings } = require('next/dist/build/swc')\n      const isLightningSupported = (await loadBindings())?.css?.lightning\n\n      if (!isLightningSupported) {\n        curLog.warn(\n          `experimental.useLightningcss is set, but the setting is disabled because next-swc/wasm does not support it yet.`\n        )\n        userConfig.experimental.useLightningcss = false\n      }\n    }\n\n    // serialize the regex config into string\n    if (userConfig?.htmlLimitedBots instanceof RegExp) {\n      // @ts-expect-error: override the htmlLimitedBots with default string, type covert: RegExp -> string\n      userConfig.htmlLimitedBots = userConfig.htmlLimitedBots.source\n    }\n\n    onLoadUserConfig?.(userConfig)\n    const completeConfig = assignDefaults(\n      dir,\n      {\n        configOrigin: relative(dir, path),\n        configFile: path,\n        configFileName,\n        ...userConfig,\n      },\n      silent\n    ) as NextConfigComplete\n    return completeConfig\n  } else {\n    const configBaseName = basename(CONFIG_FILES[0], extname(CONFIG_FILES[0]))\n    const unsupportedConfig = findUp.sync(\n      [\n        `${configBaseName}.cjs`,\n        `${configBaseName}.cts`,\n        `${configBaseName}.mts`,\n        `${configBaseName}.json`,\n        `${configBaseName}.jsx`,\n        `${configBaseName}.tsx`,\n      ],\n      { cwd: dir }\n    )\n    if (unsupportedConfig?.length) {\n      throw new Error(\n        `Configuring Next.js via '${basename(\n          unsupportedConfig\n        )}' is not supported. Please replace the file with 'next.config.js', 'next.config.mjs', or 'next.config.ts'.`\n      )\n    }\n  }\n\n  // always call assignDefaults to ensure settings like\n  // reactRoot can be updated correctly even with no next.config.js\n  const completeConfig = assignDefaults(\n    dir,\n    defaultConfig,\n    silent\n  ) as NextConfigComplete\n  completeConfig.configFileName = configFileName\n  setHttpClientAndAgentOptions(completeConfig)\n  return completeConfig\n}\n\nexport type ConfiguredExperimentalFeature =\n  | { name: keyof ExperimentalConfig; type: 'boolean'; value: boolean }\n  | { name: keyof ExperimentalConfig; type: 'number'; value: number }\n  | { name: keyof ExperimentalConfig; type: 'other' }\n\nexport function getConfiguredExperimentalFeatures(\n  userNextConfigExperimental: NextConfig['experimental']\n) {\n  const configuredExperimentalFeatures: ConfiguredExperimentalFeature[] = []\n\n  if (!userNextConfigExperimental) {\n    return configuredExperimentalFeatures\n  }\n\n  // defaultConfig.experimental is predefined and will never be undefined\n  // This is only a type guard for the typescript\n  if (defaultConfig.experimental) {\n    for (const name of Object.keys(\n      userNextConfigExperimental\n    ) as (keyof ExperimentalConfig)[]) {\n      const value = userNextConfigExperimental[name]\n\n      if (name === 'turbo' && !process.env.TURBOPACK) {\n        // Ignore any Turbopack config if Turbopack is not enabled\n        continue\n      }\n\n      if (\n        name in defaultConfig.experimental &&\n        value !== defaultConfig.experimental[name]\n      ) {\n        configuredExperimentalFeatures.push(\n          typeof value === 'boolean'\n            ? { name, type: 'boolean', value }\n            : typeof value === 'number'\n              ? { name, type: 'number', value }\n              : { name, type: 'other' }\n        )\n      }\n    }\n  }\n  return configuredExperimentalFeatures\n}\n"], "names": ["existsSync", "basename", "extname", "join", "relative", "isAbsolute", "resolve", "pathToFileURL", "findUp", "Log", "CONFIG_FILES", "PHASE_DEVELOPMENT_SERVER", "defaultConfig", "normalizeConfig", "loadWebpackHook", "imageConfigDefault", "loadEnvConfig", "updateInitialEnv", "flushAndExit", "findRootDir", "setHttpClientAndAgentOptions", "pathHasPrefix", "matchRemotePattern", "hasNextSupport", "transpileConfig", "dset", "normalizeZodErrors", "HTML_LIMITED_BOT_UA_RE_STRING", "findDir", "CanaryOnlyError", "isStableBuild", "normalizeNextConfigZodErrors", "error", "shouldExit", "issues", "flatMap", "issue", "message", "path", "warnOptionHasBeenDeprecated", "config", "nested<PERSON><PERSON><PERSON><PERSON><PERSON>", "reason", "silent", "hasWarned", "current", "found", "nestedPropertyKeys", "split", "key", "undefined", "warnOnce", "warnOptionHasBeenMovedOutOfExperimental", "oldExperimentalKey", "new<PERSON>ey", "configFileName", "experimental", "warn", "newKeys", "length", "shift", "warnCustomizedOption", "defaultValue", "customMessage", "segs", "seg", "assignDefaults", "dir", "userConfig", "result", "exportTrailingSlash", "trailingSlash", "Object", "keys", "reduce", "currentConfig", "value", "Error", "userDistDir", "trim", "Array", "isArray", "for<PERSON>ach", "ext", "constructor", "c", "k", "v", "ppr", "allowDevelopmentBuild", "process", "env", "NODE_ENV", "feature", "dynamicIO", "turbopackPersistentCaching", "nodeMiddleware", "output", "i18n", "rewrites", "redirects", "headers", "assetPrefix", "basePath", "startsWith", "endsWith", "amp", "canonicalBase", "images", "localPatterns", "hasMatch", "some", "pattern", "pathname", "search", "push", "remotePatterns", "map", "protocol", "hostname", "port", "proto", "replace", "includes", "url", "URL", "hasMatchForAssetPrefix", "domains", "loader", "loaderFile", "absolutePath", "hasWarnedBuildActivityPosition", "devIndicators", "buildActivityPosition", "position", "outputStandalone", "serverActions", "bodySizeLimit", "parseInt", "toString", "isNaN", "outputFileTracingRoot", "turbopack", "root", "NEXT_DEPLOYMENT_ID", "deploymentId", "rootDir", "hasAppDir", "Boolean", "i18nType", "locales", "defaultLocaleType", "defaultLocale", "invalidDomainItems", "filter", "item", "domain", "console", "defaultLocaleDuplicate", "find", "altItem", "hasInvalidLocale", "locale", "domainItem", "JSON", "stringify", "invalidLocales", "String", "normalizedLocales", "Set", "duplicateLocales", "localeLower", "toLowerCase", "has", "add", "size", "localeDetectionType", "localeDetection", "<PERSON><PERSON><PERSON><PERSON>", "cacheLife", "defaultDefault", "revalidate", "expire", "staleTimes", "static", "defaultCacheLifeProfile", "stale", "staticStaleTime", "expireTime", "secondsCacheLifeProfile", "dynamicStaleTime", "dynamic", "cacheHandlers", "allowedHandlerNameRegex", "handler<PERSON>eys", "invalidHandlerItems", "test", "handler<PERSON><PERSON>", "userProvidedModularizeImports", "modularizeImports", "transform", "lodash", "userProvidedOptimizePackageImports", "optimizePackageImports", "htmlLimitedBots", "useCache", "loadConfig", "phase", "customConfig", "rawConfig", "onLoadUserConfig", "reactProductionProfiling", "__NEXT_PRIVATE_RENDER_WORKER", "err", "__NEXT_PRIVATE_STANDALONE_CONFIG", "parse", "curLog", "info", "config<PERSON><PERSON><PERSON>", "cwd", "__NEXT_TEST_MODE", "userConfigModule", "envBefore", "assign", "require", "nextConfigPath", "href", "newEnv", "default", "NEXT_MINIMAL", "configSchema", "state", "safeParse", "success", "messages", "errorMessages", "target", "slice", "turbo", "loaders", "rules", "entries", "turbopackMemoryLimit", "memoryLimit", "turbopackMinify", "minify", "turbopackTreeShaking", "treeShaking", "turbopackSourceMaps", "sourceMaps", "useLightningcss", "loadBindings", "isLightningSupported", "css", "lightning", "RegExp", "source", "completeConfig", "configFile", "configBaseName", "unsupportedConfig", "sync", "getConfiguredExperimentalFeatures", "userNextConfigExperimental", "configuredExperimentalFeatures", "name", "TURBOPACK", "type"], "mappings": "AAAA,SAASA,UAAU,QAAQ,KAAI;AAC/B,SAASC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,QAAQ,OAAM;AAC7E,SAASC,aAAa,QAAQ,MAAK;AACnC,OAAOC,YAAY,6BAA4B;AAC/C,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,YAAY,EAAEC,wBAAwB,QAAQ,0BAAyB;AAChF,SAASC,aAAa,EAAEC,eAAe,QAAQ,kBAAiB;AAQhE,SAASC,eAAe,QAAQ,iBAAgB;AAChD,SAASC,kBAAkB,QAAQ,6BAA4B;AAE/D,SAASC,aAAa,EAAEC,gBAAgB,QAAQ,YAAW;AAC3D,SAASC,YAAY,QAAQ,8BAA6B;AAC1D,SAASC,WAAW,QAAQ,mBAAkB;AAC9C,SAASC,4BAA4B,QAAQ,yBAAwB;AACrE,SAASC,aAAa,QAAQ,6CAA4C;AAC1E,SAASC,kBAAkB,QAAQ,qCAAoC;AAGvE,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,eAAe,QAAQ,2CAA0C;AAC1E,SAASC,IAAI,QAAQ,qBAAoB;AACzC,SAASC,kBAAkB,QAAQ,oBAAmB;AACtD,SAASC,6BAA6B,QAAQ,oCAAmC;AACjF,SAASC,OAAO,QAAQ,wBAAuB;AAC/C,SAASC,eAAe,EAAEC,aAAa,QAAQ,4BAA2B;AAE1E,SAASjB,eAAe,QAAQ,kBAAiB;AAGjD,SAASkB,6BACPC,KAA2B;IAE3B,IAAIC,aAAa;IACjB,MAAMC,SAASR,mBAAmBM;IAClC,OAAO;QACLE,OAAOC,OAAO,CAAC,CAAC,EAAEC,KAAK,EAAEC,OAAO,EAAE;YAChC,IAAID,MAAME,IAAI,CAAC,EAAE,KAAK,UAAU;gBAC9B,oEAAoE;gBACpEL,aAAa;YACf;YAEA,OAAOI;QACT;QACAJ;KACD;AACH;AAEA,OAAO,SAASM,4BACdC,MAAkB,EAClBC,iBAAyB,EACzBC,MAAc,EACdC,MAAe;IAEf,IAAIC,YAAY;IAChB,IAAI,CAACD,QAAQ;QACX,IAAIE,UAAUL;QACd,IAAIM,QAAQ;QACZ,MAAMC,qBAAqBN,kBAAkBO,KAAK,CAAC;QACnD,KAAK,MAAMC,OAAOF,mBAAoB;YACpC,IAAIF,OAAO,CAACI,IAAI,KAAKC,WAAW;gBAC9BL,UAAUA,OAAO,CAACI,IAAI;YACxB,OAAO;gBACLH,QAAQ;gBACR;YACF;QACF;QACA,IAAIA,OAAO;YACTrC,IAAI0C,QAAQ,CAACT;YACbE,YAAY;QACd;IACF;IACA,OAAOA;AACT;AAEA,OAAO,SAASQ,wCACdZ,MAAkB,EAClBa,kBAA0B,EAC1BC,MAAc,EACdC,cAAsB,EACtBZ,MAAe;IAEf,IAAIH,OAAOgB,YAAY,IAAIH,sBAAsBb,OAAOgB,YAAY,EAAE;QACpE,IAAI,CAACb,QAAQ;YACXlC,IAAIgD,IAAI,CACN,CAAC,eAAe,EAAEJ,mBAAmB,uBAAuB,EAAEC,OAAO,IAAI,CAAC,GACxE,CAAC,mBAAmB,EAAEC,eAAe,kBAAkB,CAAC;QAE9D;QAEA,IAAIV,UAAUL;QACd,MAAMkB,UAAUJ,OAAON,KAAK,CAAC;QAC7B,MAAOU,QAAQC,MAAM,GAAG,EAAG;YACzB,MAAMV,MAAMS,QAAQE,KAAK;YACzBf,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACI,IAAI,IAAI,CAAC;YAChCJ,UAAUA,OAAO,CAACI,IAAI;QACxB;QACAJ,OAAO,CAACa,QAAQE,KAAK,GAAI,GAAG,AAACpB,OAAOgB,YAAY,AAAQ,CAACH,mBAAmB;IAC9E;IAEA,OAAOb;AACT;AAEA,SAASqB,qBACPrB,MAAkB,EAClBS,GAAW,EACXa,YAAiB,EACjBC,aAAqB,EACrBR,cAAsB,EACtBZ,MAAe;IAEf,MAAMqB,OAAOf,IAAID,KAAK,CAAC;IACvB,IAAIH,UAAUL;IAEd,MAAOwB,KAAKL,MAAM,IAAI,EAAG;QACvB,MAAMM,MAAMD,KAAKJ,KAAK;QACtB,IAAI,CAAEK,CAAAA,OAAOpB,OAAM,GAAI;YACrB;QACF;QACAA,UAAUA,OAAO,CAACoB,IAAI;IACxB;IAEA,IAAI,CAACtB,UAAUE,YAAYiB,cAAc;QACvCrD,IAAIgD,IAAI,CACN,CAAC,KAAK,EAAER,IAAI,4BAA4B,EAAEc,gBAAgBA,gBAAgB,OAAO,GAAG,+BAA+B,EAAER,eAAe,CAAC,CAAC;IAE1I;AACF;AAEA,SAASW,eACPC,GAAW,EACXC,UAAkC,EAClCzB,MAAe;QA4FX/B,6BASFyD,sBAmRAA,uBAiGOA,oCAAAA,uBA8CLA,mBAckCA,oBAKCA,oBA2LDA,wBA+DlCA,uBAmDFA;IAr0BF,MAAMd,iBAAiBa,WAAWb,cAAc;IAChD,IAAI,OAAOa,WAAWE,mBAAmB,KAAK,aAAa;QACzD,IAAI,CAAC3B,QAAQ;YACXlC,IAAIgD,IAAI,CACN,CAAC,yFAAyF,EAAEF,eAAe,CAAC,CAAC;QAEjH;QACA,IAAI,OAAOa,WAAWG,aAAa,KAAK,aAAa;YACnDH,WAAWG,aAAa,GAAGH,WAAWE,mBAAmB;QAC3D;QACA,OAAOF,WAAWE,mBAAmB;IACvC;IAEA,MAAM9B,SAASgC,OAAOC,IAAI,CAACL,YAAYM,MAAM,CAC3C,CAACC,eAAe1B;QACd,MAAM2B,QAAQR,UAAU,CAACnB,IAAI;QAE7B,IAAI2B,UAAU1B,aAAa0B,UAAU,MAAM;YACzC,OAAOD;QACT;QAEA,IAAI1B,QAAQ,WAAW;YACrB,IAAI,OAAO2B,UAAU,UAAU;gBAC7B,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,+CAA+C,EAAE,OAAOD,MAAM,CAAC,CAAC,GAD7D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAME,cAAcF,MAAMG,IAAI;YAE9B,qEAAqE;YACrE,eAAe;YACf,IAAID,gBAAgB,UAAU;gBAC5B,MAAM,qBAEL,CAFK,IAAID,MACR,CAAC,4IAA4I,CAAC,GAD1I,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,2EAA2E;YAC3E,8CAA8C;YAC9C,IAAIC,YAAYnB,MAAM,KAAK,GAAG;gBAC5B,MAAM,qBAEL,CAFK,IAAIkB,MACR,CAAC,8GAA8G,CAAC,GAD5G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAI5B,QAAQ,kBAAkB;YAC5B,IAAI,CAAC+B,MAAMC,OAAO,CAACL,QAAQ;gBACzB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,4DAA4D,EAAED,MAAM,0CAA0C,CAAC,GAD5G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAI,CAACA,MAAMjB,MAAM,EAAE;gBACjB,MAAM,qBAEL,CAFK,IAAIkB,MACR,CAAC,uGAAuG,CAAC,GADrG,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAD,MAAMM,OAAO,CAAC,CAACC;gBACb,IAAI,OAAOA,QAAQ,UAAU;oBAC3B,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,4DAA4D,EAAEM,IAAI,WAAW,EAAE,OAAOA,IAAI,0CAA0C,CAAC,GADlI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;QAEA,IAAI,CAAC,CAACP,SAASA,MAAMQ,WAAW,KAAKZ,QAAQ;YAC3CG,aAAa,CAAC1B,IAAI,GAAG;gBACnB,GAAGrC,aAAa,CAACqC,IAAI;gBACrB,GAAGuB,OAAOC,IAAI,CAACG,OAAOF,MAAM,CAAM,CAACW,GAAGC;oBACpC,MAAMC,IAAIX,KAAK,CAACU,EAAE;oBAClB,IAAIC,MAAMrC,aAAaqC,MAAM,MAAM;wBACjCF,CAAC,CAACC,EAAE,GAAGC;oBACT;oBACA,OAAOF;gBACT,GAAG,CAAC,EAAE;YACR;QACF,OAAO;YACLV,aAAa,CAAC1B,IAAI,GAAG2B;QACvB;QAEA,OAAOD;IACT,GACA,CAAC;IAGH,iDAAiD;IACjD,uEAAuE;IACvE,6CAA6C;IAC7C,KAAI/D,8BAAAA,cAAc4C,YAAY,qBAA1B5C,4BAA4B4E,GAAG,EAAE;QACnC/E,IAAIgD,IAAI,CACN,CAAC,2HAA2H,CAAC;IAEjI;IAEA,MAAMY,SAAS;QAAE,GAAGzD,aAAa;QAAE,GAAG4B,MAAM;IAAC;IAE7C,IACE6B,EAAAA,uBAAAA,OAAOb,YAAY,qBAAnBa,qBAAqBoB,qBAAqB,KAC1CC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzB;QACA,MAAM,qBAEL,CAFK,IAAIf,MACR,CAAC,sGAAsG,CAAC,GADpG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI/C,iBAAiB;YAEfuC,uBAEOA,uBAEAA,uBAIAA;QATX,oEAAoE;QACpE,KAAIA,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqBmB,GAAG,EAAE;YAC5B,MAAM,qBAAoD,CAApD,IAAI3D,gBAAgB;gBAAEgE,SAAS;YAAmB,IAAlD,qBAAA;uBAAA;4BAAA;8BAAA;YAAmD;QAC3D,OAAO,KAAIxB,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqByB,SAAS,EAAE;YACzC,MAAM,qBAA0D,CAA1D,IAAIjE,gBAAgB;gBAAEgE,SAAS;YAAyB,IAAxD,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE,OAAO,KAAIxB,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqB0B,0BAA0B,EAAE;YAC1D,MAAM,qBAEJ,CAFI,IAAIlE,gBAAgB;gBACxBgE,SAAS;YACX,IAFM,qBAAA;uBAAA;4BAAA;8BAAA;YAEL;QACH,OAAO,KAAIxB,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqB2B,cAAc,EAAE;YAC9C,MAAM,qBAA+D,CAA/D,IAAInE,gBAAgB;gBAAEgE,SAAS;YAA8B,IAA7D,qBAAA;uBAAA;4BAAA;8BAAA;YAA8D;QACtE;IACF;IAEA,IAAIxB,OAAO4B,MAAM,KAAK,UAAU;QAC9B,IAAI5B,OAAO6B,IAAI,EAAE;YACf,MAAM,qBAEL,CAFK,IAAIrB,MACR,+HADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACtD,gBAAgB;YACnB,IAAI8C,OAAO8B,QAAQ,EAAE;gBACnB1F,IAAIgD,IAAI,CACN;YAEJ;YACA,IAAIY,OAAO+B,SAAS,EAAE;gBACpB3F,IAAIgD,IAAI,CACN;YAEJ;YACA,IAAIY,OAAOgC,OAAO,EAAE;gBAClB5F,IAAIgD,IAAI,CACN;YAEJ;QACF;IACF;IAEA,IAAI,OAAOY,OAAOiC,WAAW,KAAK,UAAU;QAC1C,MAAM,qBAEL,CAFK,IAAIzB,MACR,CAAC,mDAAmD,EAAE,OAAOR,OAAOiC,WAAW,CAAC,sDAAsD,CAAC,GADnI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,OAAOjC,OAAOkC,QAAQ,KAAK,UAAU;QACvC,MAAM,qBAEL,CAFK,IAAI1B,MACR,CAAC,gDAAgD,EAAE,OAAOR,OAAOkC,QAAQ,CAAC,CAAC,CAAC,GADxE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIlC,OAAOkC,QAAQ,KAAK,IAAI;QAC1B,IAAIlC,OAAOkC,QAAQ,KAAK,KAAK;YAC3B,MAAM,qBAEL,CAFK,IAAI1B,MACR,CAAC,iFAAiF,CAAC,GAD/E,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACR,OAAOkC,QAAQ,CAACC,UAAU,CAAC,MAAM;YACpC,MAAM,qBAEL,CAFK,IAAI3B,MACR,CAAC,iDAAiD,EAAER,OAAOkC,QAAQ,CAAC,CAAC,CAAC,GADlE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIlC,OAAOkC,QAAQ,KAAK,KAAK;gBAWvBlC;YAVJ,IAAIA,OAAOkC,QAAQ,CAACE,QAAQ,CAAC,MAAM;gBACjC,MAAM,qBAEL,CAFK,IAAI5B,MACR,CAAC,iDAAiD,EAAER,OAAOkC,QAAQ,CAAC,CAAC,CAAC,GADlE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAIlC,OAAOiC,WAAW,KAAK,IAAI;gBAC7BjC,OAAOiC,WAAW,GAAGjC,OAAOkC,QAAQ;YACtC;YAEA,IAAIlC,EAAAA,cAAAA,OAAOqC,GAAG,qBAAVrC,YAAYsC,aAAa,MAAK,IAAI;gBACpCtC,OAAOqC,GAAG,CAACC,aAAa,GAAGtC,OAAOkC,QAAQ;YAC5C;QACF;IACF;IAEA,IAAIlC,0BAAAA,OAAQuC,MAAM,EAAE;QAClB,MAAMA,SAAsBvC,OAAOuC,MAAM;QAEzC,IAAI,OAAOA,WAAW,UAAU;YAC9B,MAAM,qBAEL,CAFK,IAAI/B,MACR,CAAC,8CAA8C,EAAE,OAAO+B,OAAO,6EAA6E,CAAC,GADzI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIA,OAAOC,aAAa,EAAE;YACxB,IAAI,CAAC7B,MAAMC,OAAO,CAAC2B,OAAOC,aAAa,GAAG;gBACxC,MAAM,qBAEL,CAFK,IAAIhC,MACR,CAAC,2DAA2D,EAAE,OAAO+B,OAAOC,aAAa,CAAC,6EAA6E,CAAC,GADpK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,6DAA6D;YAC7D,MAAMC,WAAWF,OAAOC,aAAa,CAACE,IAAI,CACxC,CAACC,UACCA,QAAQC,QAAQ,KAAK,4BAA4BD,QAAQE,MAAM,KAAK;YAExE,IAAI,CAACJ,UAAU;gBACb,iDAAiD;gBACjDF,OAAOC,aAAa,CAACM,IAAI,CAAC;oBACxBF,UAAU;oBACVC,QAAQ;gBACV;YACF;QACF;QAEA,IAAIN,OAAOQ,cAAc,EAAE;gBA+BrB5E;YA9BJ,IAAI,CAACwC,MAAMC,OAAO,CAAC2B,OAAOQ,cAAc,GAAG;gBACzC,MAAM,qBAEL,CAFK,IAAIvC,MACR,CAAC,4DAA4D,EAAE,OAAO+B,OAAOQ,cAAc,CAAC,6EAA6E,CAAC,GADtK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,6EAA6E;YAC7E,4EAA4E;YAC5E,0DAA0D;YAC1DR,OAAOQ,cAAc,GAAGR,OAAOQ,cAAc,CAACC,GAAG,CAC/C,CAAC,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAEP,QAAQ,EAAEC,MAAM,EAAE;gBAC7C,MAAMO,QAAQH,4BAAAA,SAAUI,OAAO,CAAC,MAAM;gBACtC,IAAI,CAAC;oBAAC;oBAAQ;oBAASxE;iBAAU,CAACyE,QAAQ,CAACF,QAAQ;oBACjD,MAAM,qBAEL,CAFK,IAAI5C,MACR,CAAC,+EAA+E,EAAE4C,MAAM,EAAE,CAAC,GADvF,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,OAAO;oBACLH,UAAUG;oBACVF;oBACAC;oBACAP;oBACAC;gBACF;YACF;YAGF,4DAA4D;YAC5D,2DAA2D;YAC3D,gBAAgB;YAChB,KAAI1E,sBAAAA,OAAO8D,WAAW,qBAAlB9D,oBAAoBgE,UAAU,CAAC,SAAS;gBAC1C,IAAI;oBACF,MAAMoB,MAAM,IAAIC,IAAIrF,OAAO8D,WAAW;oBACtC,MAAMwB,yBAAyBlB,OAAOQ,cAAc,CAACL,IAAI,CAAC,CAACC,UACzD1F,mBAAmB0F,SAASY;oBAG9B,qEAAqE;oBACrE,IAAI,CAACE,wBAAwB;wBAC3BlB,OAAOQ,cAAc,CAACD,IAAI,CAAC;4BACzBI,UAAUK,IAAIL,QAAQ;4BACtBD,UAAUM,IAAIN,QAAQ,CAACI,OAAO,CAAC,MAAM;4BACrCF,MAAMI,IAAIJ,IAAI;wBAChB;oBACF;gBACF,EAAE,OAAOxF,OAAO;oBACd,MAAM,qBAEL,CAFK,IAAI6C,MACR,CAAC,8CAA8C,EAAE7C,OAAO,GADpD,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;QAEA,IAAI4E,OAAOmB,OAAO,EAAE;YAClB,IAAI,CAAC/C,MAAMC,OAAO,CAAC2B,OAAOmB,OAAO,GAAG;gBAClC,MAAM,qBAEL,CAFK,IAAIlD,MACR,CAAC,qDAAqD,EAAE,OAAO+B,OAAOmB,OAAO,CAAC,6EAA6E,CAAC,GADxJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAI,CAACnB,OAAOoB,MAAM,EAAE;YAClBpB,OAAOoB,MAAM,GAAG;QAClB;QAEA,IACEpB,OAAOoB,MAAM,KAAK,aAClBpB,OAAOoB,MAAM,KAAK,YAClBpB,OAAOtE,IAAI,KAAKvB,mBAAmBuB,IAAI,EACvC;YACA,MAAM,qBAEL,CAFK,IAAIuC,MACR,CAAC,kCAAkC,EAAE+B,OAAOoB,MAAM,CAAC,sKAAsK,CAAC,GADtN,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IACEpB,OAAOtE,IAAI,KAAKvB,mBAAmBuB,IAAI,IACvC+B,OAAOkC,QAAQ,IACf,CAAClF,cAAcuF,OAAOtE,IAAI,EAAE+B,OAAOkC,QAAQ,GAC3C;YACAK,OAAOtE,IAAI,GAAG,GAAG+B,OAAOkC,QAAQ,GAAGK,OAAOtE,IAAI,EAAE;QAClD;QAEA,8EAA8E;QAC9E,IACEsE,OAAOtE,IAAI,IACX,CAACsE,OAAOtE,IAAI,CAACmE,QAAQ,CAAC,QACrBG,CAAAA,OAAOoB,MAAM,KAAK,aAAa3D,OAAOE,aAAa,AAAD,GACnD;YACAqC,OAAOtE,IAAI,IAAI;QACjB;QAEA,IAAIsE,OAAOqB,UAAU,EAAE;YACrB,IAAIrB,OAAOoB,MAAM,KAAK,aAAapB,OAAOoB,MAAM,KAAK,UAAU;gBAC7D,MAAM,qBAEL,CAFK,IAAInD,MACR,CAAC,kCAAkC,EAAE+B,OAAOoB,MAAM,CAAC,uFAAuF,CAAC,GADvI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAME,eAAe/H,KAAKgE,KAAKyC,OAAOqB,UAAU;YAChD,IAAI,CAACjI,WAAWkI,eAAe;gBAC7B,MAAM,qBAEL,CAFK,IAAIrD,MACR,CAAC,+CAA+C,EAAEqD,aAAa,EAAE,CAAC,GAD9D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAtB,OAAOqB,UAAU,GAAGC;QACtB;IACF;IAEArE,qBACEQ,QACA,6BACA,MACA,mGACAd,gBACAZ;IAGFJ,4BACE8B,QACA,oCACA,CAAC,yIAAyI,EAAEd,eAAe,CAAC,CAAC,EAC7JZ;IAGFJ,4BACE8B,QACA,sBACA,CAAC,8GAA8G,EAAEd,eAAe,CAAC,CAAC,EAClIZ;IAGFJ,4BACE8B,QACA,8BACA,CAAC,+FAA+F,EAAEd,eAAe,CAAC,CAAC,EACnHZ;IAGFJ,4BACE8B,QACA,+BACA,CAAC,gGAAgG,EAAEd,eAAe,CAAC,CAAC,EACpHZ;IAGF,MAAMwF,iCAAiC5F,4BACrC8B,QACA,uCACA,CAAC,2GAA2G,EAAEd,eAAe,kBAAkB,CAAC,EAChJZ;IAEF,IACEwF,kCACA9D,OAAO+D,aAAa,KAAK,WACzB/D,wBAAAA,OAAO+D,aAAa,qBAApB/D,sBAAsBgE,qBAAqB,KAC3ChE,OAAO+D,aAAa,CAACC,qBAAqB,KAAKhE,OAAO+D,aAAa,CAACE,QAAQ,EAC5E;QACA7H,IAAI0C,QAAQ,CACV,CAAC,yDAAyD,EAAEkB,OAAO+D,aAAa,CAACC,qBAAqB,CAAC,iCAAiC,EAAEhE,OAAO+D,aAAa,CAACE,QAAQ,CAAC,sCAAsC,EAAEjE,OAAO+D,aAAa,CAACC,qBAAqB,CAAC,8BAA8B,CAAC;QAE5RhE,OAAO+D,aAAa,CAACE,QAAQ,GAAGjE,OAAO+D,aAAa,CAACC,qBAAqB;IAC5E;IAEAjF,wCACEiB,QACA,wBACA,iCACAd,gBACAZ;IAEFS,wCACEiB,QACA,oCACA,0BACAd,gBACAZ;IAEFS,wCACEiB,QACA,SACA,kBACAd,gBACAZ;IAEFS,wCACEiB,QACA,oBACA,6BACAd,gBACAZ;IAEFS,wCACEiB,QACA,WACA,oBACAd,gBACAZ;IAEFS,wCACEiB,QACA,yBACA,kCACAd,gBACAZ;IAEFS,wCACEiB,QACA,iBACA,0BACAd,gBACAZ;IAEFS,wCACEiB,QACA,YACA,cACAd,gBACAZ;IAEFS,wCACEiB,QACA,yBACA,yBACAd,gBACAZ;IAEFS,wCACEiB,QACA,6BACA,6BACAd,gBACAZ;IAEFS,wCACEiB,QACA,6BACA,6BACAd,gBACAZ;IAGF,IAAI,AAAC0B,OAAOb,YAAY,CAAS+E,gBAAgB,EAAE;QACjD,IAAI,CAAC5F,QAAQ;YACXlC,IAAIgD,IAAI,CACN,CAAC,iGAAiG,CAAC;QAEvG;QACAY,OAAO4B,MAAM,GAAG;IAClB;IAEA,IACE,SAAO5B,wBAAAA,OAAOb,YAAY,sBAAnBa,qCAAAA,sBAAqBmE,aAAa,qBAAlCnE,mCAAoCoE,aAAa,MAAK,aAC7D;YAEEpE;QADF,MAAMO,QAAQ8D,UACZrE,sCAAAA,OAAOb,YAAY,CAACgF,aAAa,qBAAjCnE,oCAAmCoE,aAAa,CAACE,QAAQ;QAE3D,IAAIC,MAAMhE,UAAUA,QAAQ,GAAG;YAC7B,MAAM,qBAEL,CAFK,IAAIC,MACR,8KADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEAzB,wCACEiB,QACA,qBACA,qBACAd,gBACAZ;IAEFS,wCACEiB,QACA,8BACA,8BACAd,gBACAZ;IAEFS,wCACEiB,QACA,6BACA,6BACAd,gBACAZ;IAGF,IACE0B,CAAAA,0BAAAA,OAAQwE,qBAAqB,KAC7B,CAACxI,WAAWgE,OAAOwE,qBAAqB,GACxC;QACAxE,OAAOwE,qBAAqB,GAAGvI,QAAQ+D,OAAOwE,qBAAqB;QACnE,IAAI,CAAClG,QAAQ;YACXlC,IAAIgD,IAAI,CACN,CAAC,iDAAiD,EAAEY,OAAOwE,qBAAqB,EAAE;QAEtF;IACF;IAEA,IAAIxE,CAAAA,2BAAAA,oBAAAA,OAAQyE,SAAS,qBAAjBzE,kBAAmB0E,IAAI,KAAI,CAAC1I,WAAWgE,OAAOyE,SAAS,CAACC,IAAI,GAAG;QACjE1E,OAAOyE,SAAS,CAACC,IAAI,GAAGzI,QAAQ+D,OAAOyE,SAAS,CAACC,IAAI;QACrD,IAAI,CAACpG,QAAQ;YACXlC,IAAIgD,IAAI,CACN,CAAC,0CAA0C,EAAEY,OAAOyE,SAAS,CAACC,IAAI,EAAE;QAExE;IACF;IAEA,6BAA6B;IAC7B,IAAIrD,QAAQC,GAAG,CAACqD,kBAAkB,EAAE;QAClC3E,OAAO4E,YAAY,GAAGvD,QAAQC,GAAG,CAACqD,kBAAkB;IACtD;IAEA,IAAI3E,CAAAA,0BAAAA,OAAQwE,qBAAqB,KAAI,EAACxE,2BAAAA,qBAAAA,OAAQyE,SAAS,qBAAjBzE,mBAAmB0E,IAAI,GAAE;QAC7DtH,KAAK4C,QAAQ;YAAC;YAAa;SAAO,EAAEA,OAAOwE,qBAAqB;IAClE;IAEA,2CAA2C;IAC3C,IAAI,EAACxE,0BAAAA,OAAQwE,qBAAqB,KAAI,EAACxE,2BAAAA,qBAAAA,OAAQyE,SAAS,qBAAjBzE,mBAAmB0E,IAAI,GAAE;QAC9D,IAAIG,UAAU/H,YAAYgD;QAE1B,IAAI+E,SAAS;gBAKN7E;YAJL,IAAI,EAACA,0BAAAA,OAAQwE,qBAAqB,GAAE;gBAClCxE,OAAOwE,qBAAqB,GAAGK;YACjC;YAEA,IAAI,EAAC7E,2BAAAA,qBAAAA,OAAQyE,SAAS,qBAAjBzE,mBAAmB0E,IAAI,GAAE;gBAC5BtH,KAAK4C,QAAQ;oBAAC;oBAAa;iBAAO,EAAE6E;YACtC;QACF;IACF;IAEA9H,6BAA6BiD,UAAUzD;IAEvC,IAAIyD,OAAO6B,IAAI,EAAE;QACf,MAAMiD,YAAYC,QAAQxH,QAAQuC,KAAK;QAEvC,IAAIgF,WAAW;YACb5G,4BACE8B,QACA,QACA,CAAC,sBAAsB,EAAEd,eAAe,uKAAuK,CAAC,EAChNZ;QAEJ;QAEA,MAAM,EAAEuD,IAAI,EAAE,GAAG7B;QACjB,MAAMgF,WAAW,OAAOnD;QAExB,IAAImD,aAAa,UAAU;YACzB,MAAM,qBAEL,CAFK,IAAIxE,MACR,CAAC,4CAA4C,EAAEwE,SAAS,2EAA2E,CAAC,GADhI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACrE,MAAMC,OAAO,CAACiB,KAAKoD,OAAO,GAAG;YAChC,MAAM,qBAEL,CAFK,IAAIzE,MACR,CAAC,mDAAmD,EAAE,OAAOqB,KAAKoD,OAAO,CAAC,2EAA2E,CAAC,GADlJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIpD,KAAKoD,OAAO,CAAC3F,MAAM,GAAG,OAAO,CAAChB,QAAQ;YACxClC,IAAIgD,IAAI,CACN,CAAC,SAAS,EAAEyC,KAAKoD,OAAO,CAAC3F,MAAM,CAAC,mLAAmL,CAAC;QAExN;QAEA,MAAM4F,oBAAoB,OAAOrD,KAAKsD,aAAa;QAEnD,IAAI,CAACtD,KAAKsD,aAAa,IAAID,sBAAsB,UAAU;YACzD,MAAM,qBAEL,CAFK,IAAI1E,MACR,CAAC,0HAA0H,CAAC,GADxH,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,OAAOqB,KAAK6B,OAAO,KAAK,eAAe,CAAC/C,MAAMC,OAAO,CAACiB,KAAK6B,OAAO,GAAG;YACvE,MAAM,qBAEL,CAFK,IAAIlD,MACR,CAAC,2IAA2I,EAAE,OAAOqB,KAAK6B,OAAO,CAAC,2EAA2E,CAAC,GAD1O,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI7B,KAAK6B,OAAO,EAAE;YAChB,MAAM0B,qBAAqBvD,KAAK6B,OAAO,CAAC2B,MAAM,CAAC,CAACC;oBAYfzD;gBAX/B,IAAI,CAACyD,QAAQ,OAAOA,SAAS,UAAU,OAAO;gBAC9C,IAAI,CAACA,KAAKH,aAAa,EAAE,OAAO;gBAChC,IAAI,CAACG,KAAKC,MAAM,IAAI,OAAOD,KAAKC,MAAM,KAAK,UAAU,OAAO;gBAE5D,IAAID,KAAKC,MAAM,CAACjC,QAAQ,CAAC,MAAM;oBAC7BkC,QAAQpG,IAAI,CACV,CAAC,cAAc,EAAEkG,KAAKC,MAAM,CAAC,2GAA2G,CAAC;oBAE3I,OAAO;gBACT;gBAEA,MAAME,0BAAyB5D,gBAAAA,KAAK6B,OAAO,qBAAZ7B,cAAc6D,IAAI,CAC/C,CAACC,UACCA,QAAQR,aAAa,KAAKG,KAAKH,aAAa,IAC5CQ,QAAQJ,MAAM,KAAKD,KAAKC,MAAM;gBAGlC,IAAI,CAACjH,UAAUmH,wBAAwB;oBACrCD,QAAQpG,IAAI,CACV,CAAC,KAAK,EAAEkG,KAAKC,MAAM,CAAC,KAAK,EAAEE,uBAAuBF,MAAM,CAAC,8BAA8B,EAAED,KAAKH,aAAa,CAAC,+DAA+D,CAAC;oBAE9K,OAAO;gBACT;gBAEA,IAAIS,mBAAmB;gBAEvB,IAAIjF,MAAMC,OAAO,CAAC0E,KAAKL,OAAO,GAAG;oBAC/B,KAAK,MAAMY,UAAUP,KAAKL,OAAO,CAAE;wBACjC,IAAI,OAAOY,WAAW,UAAUD,mBAAmB;wBAEnD,KAAK,MAAME,cAAcjE,KAAK6B,OAAO,IAAI,EAAE,CAAE;4BAC3C,IAAIoC,eAAeR,MAAM;4BACzB,IAAIQ,WAAWb,OAAO,IAAIa,WAAWb,OAAO,CAAC3B,QAAQ,CAACuC,SAAS;gCAC7DL,QAAQpG,IAAI,CACV,CAAC,KAAK,EAAEkG,KAAKC,MAAM,CAAC,KAAK,EAAEO,WAAWP,MAAM,CAAC,wBAAwB,EAAEM,OAAO,sEAAsE,CAAC;gCAEvJD,mBAAmB;gCACnB;4BACF;wBACF;oBACF;gBACF;gBAEA,OAAOA;YACT;YAEA,IAAIR,mBAAmB9F,MAAM,GAAG,GAAG;gBACjC,MAAM,qBAML,CANK,IAAIkB,MACR,CAAC,8BAA8B,EAAE4E,mBAC9BpC,GAAG,CAAC,CAACsC,OAAcS,KAAKC,SAAS,CAACV,OAClCxJ,IAAI,CACH,MACA,8KAA8K,CAAC,GAL/K,qBAAA;2BAAA;gCAAA;kCAAA;gBAMN;YACF;QACF;QAEA,IAAI,CAAC6E,MAAMC,OAAO,CAACiB,KAAKoD,OAAO,GAAG;YAChC,MAAM,qBAEL,CAFK,IAAIzE,MACR,CAAC,2FAA2F,EAAE,OAAOqB,KAAKoD,OAAO,CAAC,2EAA2E,CAAC,GAD1L,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMgB,iBAAiBpE,KAAKoD,OAAO,CAACI,MAAM,CACxC,CAACQ,SAAgB,OAAOA,WAAW;QAGrC,IAAII,eAAe3G,MAAM,GAAG,GAAG;YAC7B,MAAM,qBAOL,CAPK,IAAIkB,MACR,CAAC,gDAAgD,EAAEyF,eAChDjD,GAAG,CAACkD,QACJpK,IAAI,CACH,MACA,wEAAwE,CAAC,GAC3E,CAAC,+HAA+H,CAAC,GAN/H,qBAAA;uBAAA;4BAAA;8BAAA;YAON;QACF;QAEA,IAAI,CAAC+F,KAAKoD,OAAO,CAAC3B,QAAQ,CAACzB,KAAKsD,aAAa,GAAG;YAC9C,MAAM,qBAEL,CAFK,IAAI3E,MACR,CAAC,0IAA0I,CAAC,GADxI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM2F,oBAAoB,IAAIC;QAC9B,MAAMC,mBAAmB,IAAID;QAE7BvE,KAAKoD,OAAO,CAACpE,OAAO,CAAC,CAACgF;YACpB,MAAMS,cAAcT,OAAOU,WAAW;YACtC,IAAIJ,kBAAkBK,GAAG,CAACF,cAAc;gBACtCD,iBAAiBI,GAAG,CAACZ;YACvB;YACAM,kBAAkBM,GAAG,CAACH;QACxB;QAEA,IAAID,iBAAiBK,IAAI,GAAG,GAAG;YAC7B,MAAM,qBAKL,CALK,IAAIlG,MACR,CAAC,kEAAkE,CAAC,GAClE,GAAG;mBAAI6F;aAAiB,CAACvK,IAAI,CAAC,MAAM,EAAE,CAAC,GACvC,CAAC,yCAAyC,CAAC,GAC3C,CAAC,wEAAwE,CAAC,GAJxE,qBAAA;uBAAA;4BAAA;8BAAA;YAKN;QACF;QAEA,2CAA2C;QAC3C+F,KAAKoD,OAAO,GAAG;YACbpD,KAAKsD,aAAa;eACftD,KAAKoD,OAAO,CAACI,MAAM,CAAC,CAACQ,SAAWA,WAAWhE,KAAKsD,aAAa;SACjE;QAED,MAAMwB,sBAAsB,OAAO9E,KAAK+E,eAAe;QAEvD,IACED,wBAAwB,aACxBA,wBAAwB,aACxB;YACA,MAAM,qBAEL,CAFK,IAAInG,MACR,CAAC,yEAAyE,EAAEmG,oBAAoB,2EAA2E,CAAC,GADxK,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAI3G,OAAO+D,aAAa,KAAK,WAAS/D,yBAAAA,OAAO+D,aAAa,qBAApB/D,uBAAsBiE,QAAQ,GAAE;QACpE,MAAM,EAAEA,QAAQ,EAAE,GAAGjE,OAAO+D,aAAa;QACzC,MAAM8C,gBAAgB;YACpB;YACA;YACA;YACA;SACD;QAED,IAAI,CAACA,cAAcvD,QAAQ,CAACW,WAAW;YACrC,MAAM,qBAIL,CAJK,IAAIzD,MACR,CAAC,0DAA0D,EAAEqG,cAAc/K,IAAI,CAC7E,MACA,WAAW,EAAEmI,UAAU,GAHrB,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,IAAIjE,OAAOb,YAAY,EAAE;YAElB5C,8BAGkBA,uCAAAA,8BAKpBA,wCAAAA;QATHyD,OAAOb,YAAY,CAAC2H,SAAS,GAAG;gBAC3BvK,+BAAAA,cAAc4C,YAAY,qBAA1B5C,6BAA4BuK,SAAS,AAAxC;YACA,GAAG9G,OAAOb,YAAY,CAAC2H,SAAS;QAClC;QACA,MAAMC,kBAAiBxK,+BAAAA,cAAc4C,YAAY,sBAA1B5C,wCAAAA,6BAA4BuK,SAAS,qBAArCvK,qCAAuC,CAAC,UAAU;QACzE,IACE,CAACwK,kBACDA,eAAeC,UAAU,KAAKnI,aAC9BkI,eAAeE,MAAM,KAAKpI,aAC1B,GAACtC,+BAAAA,cAAc4C,YAAY,sBAA1B5C,yCAAAA,6BAA4B2K,UAAU,qBAAtC3K,uCAAwC4K,MAAM,GAC/C;YACA,MAAM,qBAA0C,CAA1C,IAAI3G,MAAM,kCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyC;QACjD;QACA,MAAM4G,0BAA0BpH,OAAOb,YAAY,CAAC2H,SAAS,CAAC,UAAU;QACxE,IAAI,CAACM,yBAAyB;YAC5BpH,OAAOb,YAAY,CAAC2H,SAAS,CAAC,UAAU,GAAGC;QAC7C,OAAO;YACL,IAAIK,wBAAwBC,KAAK,KAAKxI,WAAW;oBACvBmB,iCAEHzD,yCAAAA;gBAFrB,MAAM+K,mBAAkBtH,kCAAAA,OAAOb,YAAY,CAAC+H,UAAU,qBAA9BlH,gCAAgCmH,MAAM;gBAC9DC,wBAAwBC,KAAK,GAC3BC,qBAAmB/K,+BAAAA,cAAc4C,YAAY,sBAA1B5C,0CAAAA,6BAA4B2K,UAAU,qBAAtC3K,wCAAwC4K,MAAM;YACrE;YACA,IAAIC,wBAAwBJ,UAAU,KAAKnI,WAAW;gBACpDuI,wBAAwBJ,UAAU,GAAGD,eAAeC,UAAU;YAChE;YACA,IAAII,wBAAwBH,MAAM,KAAKpI,WAAW;gBAChDuI,wBAAwBH,MAAM,GAC5BjH,OAAOuH,UAAU,IAAIR,eAAeE,MAAM;YAC9C;QACF;QACA,+CAA+C;QAC/C,MAAMO,0BAA0BxH,OAAOb,YAAY,CAAC2H,SAAS,CAAC,UAAU;QACxE,IACEU,2BACAA,wBAAwBH,KAAK,KAAKxI,WAClC;gBAGyBmB,kCAEHzD,yCAAAA;YAJtB,iFAAiF;YACjF,wDAAwD;YACxD,MAAMkL,oBAAmBzH,mCAAAA,OAAOb,YAAY,CAAC+H,UAAU,qBAA9BlH,iCAAgC0H,OAAO;YAChEF,wBAAwBH,KAAK,GAC3BI,sBAAoBlL,+BAAAA,cAAc4C,YAAY,sBAA1B5C,0CAAAA,6BAA4B2K,UAAU,qBAAtC3K,wCAAwCmL,OAAO;QACvE;IACF;IAEA,KAAI1H,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqB2H,aAAa,EAAE;QACtC,MAAMC,0BAA0B;QAEhC,IAAI,OAAO5H,OAAOb,YAAY,CAACwI,aAAa,KAAK,UAAU;YACzD,MAAM,qBAEL,CAFK,IAAInH,MACR,CAAC,+GAA+G,EAAEuF,KAAKC,SAAS,CAAChG,OAAOb,YAAY,CAACwI,aAAa,GAAG,GADjK,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAME,cAAc1H,OAAOC,IAAI,CAACJ,OAAOb,YAAY,CAACwI,aAAa;QACjE,MAAMG,sBAA8D,EAAE;QAEtE,KAAK,MAAMlJ,OAAOiJ,YAAa;YAC7B,IAAI,CAACD,wBAAwBG,IAAI,CAACnJ,MAAM;gBACtCkJ,oBAAoBhF,IAAI,CAAC;oBACvBlE;oBACAP,QAAQ;gBACV;YACF,OAAO;gBACL,MAAM2J,cAAchI,OAAOb,YAAY,CAACwI,aAAa,CAAC/I,IAAI;gBAE1D,IAAIoJ,eAAe,CAACrM,WAAWqM,cAAc;oBAC3CF,oBAAoBhF,IAAI,CAAC;wBACvBlE;wBACAP,QAAQ,CAAC,qDAAqD,EAAE2J,aAAa;oBAC/E;gBACF;YACF;YACA,IAAIF,oBAAoBxI,MAAM,EAAE;gBAC9B,MAAM,qBAEL,CAFK,IAAIkB,MACR,CAAC,oEAAoE,EAAEsH,oBAAoB9E,GAAG,CAAC,CAACsC,OAAS,GAAG1G,IAAI,EAAE,EAAE0G,KAAKjH,MAAM,EAAE,EAAEvC,IAAI,CAAC,OAAO,GAD3I,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMmM,gCAAgCjI,OAAOkI,iBAAiB;IAC9D,kJAAkJ;IAClJ,6EAA6E;IAC7ElI,OAAOkI,iBAAiB,GAAG;QACzB,GAAID,iCAAiC,CAAC,CAAC;QACvC,gFAAgF;QAChF,uBAAuB;YACrBE,WAAW;QACb;QACAC,QAAQ;YACND,WAAW;QACb;IACF;IAEA,MAAME,qCACJrI,EAAAA,wBAAAA,OAAOb,YAAY,qBAAnBa,sBAAqBsI,sBAAsB,KAAI,EAAE;IACnD,IAAI,CAACtI,OAAOb,YAAY,EAAE;QACxBa,OAAOb,YAAY,GAAG,CAAC;IACzB;IAEAa,OAAOb,YAAY,CAACmJ,sBAAsB,GAAG;WACxC,IAAIlC,IAAI;eACNiC;YACH;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,4EAA4E;YAC5E,mCAAmC;YACnC,0EAA0E;YAC1E,wBAAwB;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;KACF;IAED,IAAI,CAACrI,OAAOuI,eAAe,EAAE;QAC3B,oGAAoG;QACpGvI,OAAOuI,eAAe,GAAGjL;IAC3B;IAEA,4EAA4E;IAC5E,8EAA8E;IAC9E,2BAA2B;IAC3B,IAAI0C,OAAOb,YAAY,CAACqJ,QAAQ,KAAK3J,WAAW;QAC9CmB,OAAOb,YAAY,CAACqJ,QAAQ,GAAGxI,OAAOb,YAAY,CAACsC,SAAS;IAC9D;IAEA,OAAOzB;AACT;AAEA,eAAe,eAAeyI,WAC5BC,KAAa,EACb5I,GAAW,EACX,EACE6I,YAAY,EACZC,SAAS,EACTtK,SAAS,IAAI,EACbuK,gBAAgB,EAChBC,wBAAwB,EAOzB,GAAG,CAAC,CAAC;IAEN,IAAI,CAACzH,QAAQC,GAAG,CAACyH,4BAA4B,EAAE;QAC7C,IAAI;YACFtM;QACF,EAAE,OAAOuM,KAAK;YACZ,gDAAgD;YAChD,yBAAyB;YACzB,IAAI,CAAC3H,QAAQC,GAAG,CAAC2H,gCAAgC,EAAE;gBACjD,MAAMD;YACR;QACF;IACF;IAEA,IAAI3H,QAAQC,GAAG,CAAC2H,gCAAgC,EAAE;QAChD,OAAOlD,KAAKmD,KAAK,CAAC7H,QAAQC,GAAG,CAAC2H,gCAAgC;IAChE;IAEA,MAAME,SAAS7K,SACX;QACEc,MAAM,KAAO;QACbgK,MAAM,KAAO;QACbzL,OAAO,KAAO;IAChB,IACAvB;IAEJO,cAAcmD,KAAK4I,UAAUpM,0BAA0B6M;IAEvD,IAAIjK,iBAAiB;IAErB,IAAIyJ,cAAc;QAChB,OAAO9I,eACLC,KACA;YACEuJ,cAAc;YACdnK;YACA,GAAGyJ,YAAY;QACjB,GACArK;IAEJ;IAEA,MAAML,OAAO,MAAM9B,OAAOE,cAAc;QAAEiN,KAAKxJ;IAAI;IAEnD,IAAIuB,QAAQC,GAAG,CAACiI,gBAAgB,EAAE;QAChC,IAAItL,MAAM;YACR7B,IAAIgN,IAAI,CAAC,CAAC,oBAAoB,EAAEnL,MAAM;QACxC,OAAO;YACL7B,IAAIgN,IAAI,CAAC;QACX;IACF;IAEA,2BAA2B;IAC3B,IAAInL,wBAAAA,KAAMqB,MAAM,EAAE;YA2FZS,iBAcFA,gCAAAA,0BACCA,iCAAAA,2BAmBCA,2BAoBAA;QAhJJb,iBAAiBtD,SAASqC;QAE1B,IAAIuL;QACJ,IAAI;YACF,MAAMC,YAAYtJ,OAAOuJ,MAAM,CAAC,CAAC,GAAGrI,QAAQC,GAAG;YAE/C,uEAAuE;YACvE,sEAAsE;YACtE,8BAA8B;YAC9B,IAAID,QAAQC,GAAG,CAACiI,gBAAgB,KAAK,QAAQ;gBAC3C,4DAA4D;gBAC5D,0DAA0D;gBAC1D,8CAA8C;gBAC9CC,mBAAmBG,QAAQ1L;YAC7B,OAAO,IAAIiB,mBAAmB,kBAAkB;gBAC9CsK,mBAAmB,MAAMrM,gBAAgB;oBACvCyM,gBAAgB3L;oBAChBqL,KAAKxJ;gBACP;YACF,OAAO;gBACL0J,mBAAmB,MAAM,MAAM,CAACtN,cAAc+B,MAAM4L,IAAI;YAC1D;YACA,MAAMC,SAA6B,CAAC;YAEpC,KAAK,MAAMlL,OAAOuB,OAAOC,IAAI,CAACiB,QAAQC,GAAG,EAAG;gBAC1C,IAAImI,SAAS,CAAC7K,IAAI,KAAKyC,QAAQC,GAAG,CAAC1C,IAAI,EAAE;oBACvCkL,MAAM,CAAClL,IAAI,GAAGyC,QAAQC,GAAG,CAAC1C,IAAI;gBAChC;YACF;YACAhC,iBAAiBkN;YAEjB,IAAIlB,WAAW;gBACb,OAAOY;YACT;QACF,EAAE,OAAOR,KAAK;YACZ,0EAA0E;YAC1EG,OAAOxL,KAAK,CACV,CAAC,eAAe,EAAEuB,eAAe,uEAAuE,CAAC;YAE3G,MAAM8J;QACR;QAEA,MAAMjJ,aAAc,MAAMvD,gBACxBkM,OACAc,iBAAiBO,OAAO,IAAIP;QAG9B,IAAI,CAACnI,QAAQC,GAAG,CAAC0I,YAAY,EAAE;YAC7B,iEAAiE;YACjE,MAAM,EAAEC,YAAY,EAAE,GACpBN,QAAQ;YACV,MAAMO,QAAQD,aAAaE,SAAS,CAACpK;YAErC,IAAImK,MAAME,OAAO,KAAK,OAAO;gBAC3B,uBAAuB;gBACvB,MAAMC,WAAW;oBAAC,CAAC,QAAQ,EAAEnL,eAAe,mBAAmB,CAAC;iBAAC;gBAEjE,MAAM,CAACoL,eAAe1M,WAAW,GAAGF,6BAClCwM,MAAMvM,KAAK;gBAEb,kBAAkB;gBAClB,KAAK,MAAMA,SAAS2M,cAAe;oBACjCD,SAASvH,IAAI,CAAC,CAAC,IAAI,EAAEnF,OAAO;gBAC9B;gBAEA,uBAAuB;gBACvB0M,SAASvH,IAAI,CACX;gBAGF,IAAIlF,YAAY;oBACd,KAAK,MAAMI,WAAWqM,SAAU;wBAC9B7E,QAAQ7H,KAAK,CAACK;oBAChB;oBACA,MAAMnB,aAAa;gBACrB,OAAO;oBACL,KAAK,MAAMmB,WAAWqM,SAAU;wBAC9BlB,OAAO/J,IAAI,CAACpB;oBACd;gBACF;YACF;QACF;QAEA,IAAI+B,WAAWwK,MAAM,IAAIxK,WAAWwK,MAAM,KAAK,UAAU;YACvD,MAAM,qBAGL,CAHK,IAAI/J,MACR,CAAC,gDAAgD,EAAEtB,eAAe,GAAG,CAAC,GACpE,iFAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,KAAIa,kBAAAA,WAAWsC,GAAG,qBAAdtC,gBAAgBuC,aAAa,EAAE;YACjC,MAAM,EAAEA,aAAa,EAAE,GAAGvC,WAAWsC,GAAG,IAAK,CAAC;YAC9CtC,WAAWsC,GAAG,GAAGtC,WAAWsC,GAAG,IAAI,CAAC;YACpCtC,WAAWsC,GAAG,CAACC,aAAa,GAC1B,AAACA,CAAAA,CAAAA,iCAAAA,cAAeF,QAAQ,CAAC,QACrBE,cAAckI,KAAK,CAAC,GAAG,CAAC,KACxBlI,aAAY,KAAM;QAC1B;QAEA,IAAIwG,0BAA0B;YAC5B/I,WAAW+I,wBAAwB,GAAGA;QACxC;QAEA,IACE/I,EAAAA,2BAAAA,WAAWZ,YAAY,sBAAvBY,iCAAAA,yBAAyB0K,KAAK,qBAA9B1K,+BAAgC2K,OAAO,KACvC,GAAC3K,4BAAAA,WAAWZ,YAAY,sBAAvBY,kCAAAA,0BAAyB0K,KAAK,qBAA9B1K,gCAAgC4K,KAAK,GACtC;YACAxB,OAAO/J,IAAI,CACT,sIACE,uFACA,4FACA;YAGJ,MAAMuL,QAA+C,CAAC;YACtD,KAAK,MAAM,CAAC7J,KAAK4J,QAAQ,IAAIvK,OAAOyK,OAAO,CACzC7K,WAAWZ,YAAY,CAACsL,KAAK,CAACC,OAAO,EACpC;gBACDC,KAAK,CAAC,MAAM7J,IAAI,GAAG4J;YACrB;YAEA3K,WAAWZ,YAAY,CAACsL,KAAK,CAACE,KAAK,GAAGA;QACxC;QAEA,KAAI5K,4BAAAA,WAAWZ,YAAY,qBAAvBY,0BAAyB0K,KAAK,EAAE;YAClCtB,OAAO/J,IAAI,CACT;YAGF,kEAAkE;YAClEW,WAAW0E,SAAS,GAAG;gBACrB,GAAG1E,WAAWZ,YAAY,CAACsL,KAAK;gBAChC,GAAG1K,WAAW0E,SAAS;YACzB;YACA1E,WAAWZ,YAAY,CAAC0L,oBAAoB,KAC1C9K,WAAWZ,YAAY,CAACsL,KAAK,CAACK,WAAW;YAC3C/K,WAAWZ,YAAY,CAAC4L,eAAe,KACrChL,WAAWZ,YAAY,CAACsL,KAAK,CAACO,MAAM;YACtCjL,WAAWZ,YAAY,CAAC8L,oBAAoB,KAC1ClL,WAAWZ,YAAY,CAACsL,KAAK,CAACS,WAAW;YAC3CnL,WAAWZ,YAAY,CAACgM,mBAAmB,KACzCpL,WAAWZ,YAAY,CAACsL,KAAK,CAACW,UAAU;QAC5C;QAEA,KAAIrL,4BAAAA,WAAWZ,YAAY,qBAAvBY,0BAAyBsL,eAAe,EAAE;gBAEf,MAAC;YAD9B,MAAM,EAAEC,YAAY,EAAE,GAAG3B,QAAQ;YACjC,MAAM4B,wBAAwB,QAAA,MAAMD,oCAAP,OAAA,AAAC,MAAuBE,GAAG,qBAA3B,KAA6BC,SAAS;YAEnE,IAAI,CAACF,sBAAsB;gBACzBpC,OAAO/J,IAAI,CACT,CAAC,+GAA+G,CAAC;gBAEnHW,WAAWZ,YAAY,CAACkM,eAAe,GAAG;YAC5C;QACF;QAEA,yCAAyC;QACzC,IAAItL,CAAAA,8BAAAA,WAAYwI,eAAe,aAAYmD,QAAQ;YACjD,oGAAoG;YACpG3L,WAAWwI,eAAe,GAAGxI,WAAWwI,eAAe,CAACoD,MAAM;QAChE;QAEA9C,oCAAAA,iBAAmB9I;QACnB,MAAM6L,iBAAiB/L,eACrBC,KACA;YACEuJ,cAActN,SAAS+D,KAAK7B;YAC5B4N,YAAY5N;YACZiB;YACA,GAAGa,UAAU;QACf,GACAzB;QAEF,OAAOsN;IACT,OAAO;QACL,MAAME,iBAAiBlQ,SAASS,YAAY,CAAC,EAAE,EAAER,QAAQQ,YAAY,CAAC,EAAE;QACxE,MAAM0P,oBAAoB5P,OAAO6P,IAAI,CACnC;YACE,GAAGF,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,KAAK,CAAC;YACxB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;SACxB,EACD;YAAExC,KAAKxJ;QAAI;QAEb,IAAIiM,qCAAAA,kBAAmBzM,MAAM,EAAE;YAC7B,MAAM,qBAIL,CAJK,IAAIkB,MACR,CAAC,yBAAyB,EAAE5E,SAC1BmQ,mBACA,0GAA0G,CAAC,GAHzG,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,qDAAqD;IACrD,iEAAiE;IACjE,MAAMH,iBAAiB/L,eACrBC,KACAvD,eACA+B;IAEFsN,eAAe1M,cAAc,GAAGA;IAChCnC,6BAA6B6O;IAC7B,OAAOA;AACT;AAOA,OAAO,SAASK,kCACdC,0BAAsD;IAEtD,MAAMC,iCAAkE,EAAE;IAE1E,IAAI,CAACD,4BAA4B;QAC/B,OAAOC;IACT;IAEA,uEAAuE;IACvE,+CAA+C;IAC/C,IAAI5P,cAAc4C,YAAY,EAAE;QAC9B,KAAK,MAAMiN,QAAQjM,OAAOC,IAAI,CAC5B8L,4BACiC;YACjC,MAAM3L,QAAQ2L,0BAA0B,CAACE,KAAK;YAE9C,IAAIA,SAAS,WAAW,CAAC/K,QAAQC,GAAG,CAAC+K,SAAS,EAAE;gBAE9C;YACF;YAEA,IACED,QAAQ7P,cAAc4C,YAAY,IAClCoB,UAAUhE,cAAc4C,YAAY,CAACiN,KAAK,EAC1C;gBACAD,+BAA+BrJ,IAAI,CACjC,OAAOvC,UAAU,YACb;oBAAE6L;oBAAME,MAAM;oBAAW/L;gBAAM,IAC/B,OAAOA,UAAU,WACf;oBAAE6L;oBAAME,MAAM;oBAAU/L;gBAAM,IAC9B;oBAAE6L;oBAAME,MAAM;gBAAQ;YAEhC;QACF;IACF;IACA,OAAOH;AACT"}