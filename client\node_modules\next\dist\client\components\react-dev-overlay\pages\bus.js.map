{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/bus.ts"], "sourcesContent": ["import type { BusEvent } from '../shared'\n\nexport type BusEventHandler = (ev: BusEvent) => void\n\nlet handlers: Set<BusEventHandler> = new Set()\nlet queue: BusEvent[] = []\n\nfunction drain() {\n  // Draining should never happen synchronously in case multiple handlers are\n  // registered.\n  setTimeout(function () {\n    while (\n      // Until we are out of events:\n      Boolean(queue.length) &&\n      // Or, if all handlers removed themselves as a result of handling the\n      // event(s)\n      Boolean(handlers.size)\n    ) {\n      const ev = queue.shift()!\n      handlers.forEach((handler) => handler(ev))\n    }\n  }, 1)\n}\n\nexport function emit(ev: BusEvent): void {\n  queue.push(Object.freeze({ ...ev }))\n  drain()\n}\n\nexport function on(fn: BusEventHandler): boolean {\n  if (handlers.has(fn)) {\n    return false\n  }\n\n  handlers.add(fn)\n  drain()\n  return true\n}\n\nexport function off(fn: BusEventHandler): boolean {\n  if (handlers.has(fn)) {\n    handlers.delete(fn)\n    return true\n  }\n\n  return false\n}\n"], "names": ["emit", "off", "on", "handlers", "Set", "queue", "drain", "setTimeout", "Boolean", "length", "size", "ev", "shift", "for<PERSON>ach", "handler", "push", "Object", "freeze", "fn", "has", "add", "delete"], "mappings": ";;;;;;;;;;;;;;;;IAwBgBA,IAAI;eAAJA;;IAeAC,GAAG;eAAHA;;IAVAC,EAAE;eAAFA;;;AAzBhB,IAAIC,WAAiC,IAAIC;AACzC,IAAIC,QAAoB,EAAE;AAE1B,SAASC;IACP,2EAA2E;IAC3E,cAAc;IACdC,WAAW;QACT,MACE,8BAA8B;QAC9BC,QAAQH,MAAMI,MAAM,KACpB,qEAAqE;QACrE,WAAW;QACXD,QAAQL,SAASO,IAAI,EACrB;YACA,MAAMC,KAAKN,MAAMO,KAAK;YACtBT,SAASU,OAAO,CAAC,CAACC,UAAYA,QAAQH;QACxC;IACF,GAAG;AACL;AAEO,SAASX,KAAKW,EAAY;IAC/BN,MAAMU,IAAI,CAACC,OAAOC,MAAM,CAAC;QAAE,GAAGN,EAAE;IAAC;IACjCL;AACF;AAEO,SAASJ,GAAGgB,EAAmB;IACpC,IAAIf,SAASgB,GAAG,CAACD,KAAK;QACpB,OAAO;IACT;IAEAf,SAASiB,GAAG,CAACF;IACbZ;IACA,OAAO;AACT;AAEO,SAASL,IAAIiB,EAAmB;IACrC,IAAIf,SAASgB,GAAG,CAACD,KAAK;QACpBf,SAASkB,MAAM,CAACH;QAChB,OAAO;IACT;IAEA,OAAO;AACT"}