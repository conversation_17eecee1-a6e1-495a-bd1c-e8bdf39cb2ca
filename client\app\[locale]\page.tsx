'use client';

import { useParams } from 'next/navigation';
import { Locale } from '../../lib/i18n';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import HeroSection from '../../components/HeroSection';
import FeaturedProducts from '../../components/FeaturedProducts';
import CategoriesSection from '../../components/CategoriesSection';
import WhatsAppButton from '../../components/WhatsAppButton';
import ServicesSection from '../../components/ServicesSection';
import PartnersSection from '../../components/PartnersSection';

export default function HomePage() {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;

  return (
    <>
      <Navbar locale={locale} />
      <main>
        <HeroSection locale={locale} />
        <ServicesSection locale={locale} />
        <CategoriesSection locale={locale} />
        <FeaturedProducts locale={locale} />
        <PartnersSection locale={locale} />
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
