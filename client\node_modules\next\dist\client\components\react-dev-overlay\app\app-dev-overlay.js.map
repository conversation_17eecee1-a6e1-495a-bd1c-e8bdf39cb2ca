{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/app-dev-overlay.tsx"], "sourcesContent": ["import type { OverlayState } from '../shared'\nimport type { GlobalErrorComponent } from '../../error-boundary'\n\nimport { useCallback, useEffect, useState } from 'react'\nimport { AppDevOverlayErrorBoundary } from './app-dev-overlay-error-boundary'\nimport { FontStyles } from '../font/font-styles'\nimport { DevOverlay } from '../ui/dev-overlay'\nimport { handleClientError } from '../../errors/use-error-handler'\nimport { isNextRouterError } from '../../is-next-router-error'\nimport { MISSING_ROOT_TAGS_ERROR } from '../../../../shared/lib/errors/constants'\n\nfunction readSsrError(): (Error & { digest?: string }) | null {\n  if (typeof document === 'undefined') {\n    return null\n  }\n\n  const ssrErrorTemplateTag = document.querySelector(\n    'template[data-next-error-message]'\n  )\n  if (ssrErrorTemplateTag) {\n    const message: string = ssrErrorTemplateTag.getAttribute(\n      'data-next-error-message'\n    )!\n    const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack')\n    const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest')\n    const error = new Error(message)\n    if (digest) {\n      ;(error as any).digest = digest\n    }\n    // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n    if (isNextRouterError(error)) {\n      return null\n    }\n    error.stack = stack || ''\n    return error\n  }\n\n  return null\n}\n\n// Needs to be in the same error boundary as the shell.\n// If it commits, we know we recovered from an SSR error.\n// If it doesn't commit, we errored again and React will take care of error reporting.\nfunction ReplaySsrOnlyErrors({\n  onBlockingError,\n}: {\n  onBlockingError: () => void\n}) {\n  if (process.env.NODE_ENV !== 'production') {\n    // Need to read during render. The attributes will be gone after commit.\n    const ssrError = readSsrError()\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      if (ssrError !== null) {\n        // TODO(veil): Produces wrong Owner Stack\n        // TODO(veil): Mark as recoverable error\n        // TODO(veil): console.error\n        handleClientError(ssrError)\n\n        // If it's missing root tags, we can't recover, make it blocking.\n        if (ssrError.digest === MISSING_ROOT_TAGS_ERROR) {\n          onBlockingError()\n        }\n      }\n    }, [ssrError, onBlockingError])\n  }\n\n  return null\n}\n\nexport function AppDevOverlay({\n  state,\n  globalError,\n  children,\n}: {\n  state: OverlayState\n  globalError: [GlobalErrorComponent, React.ReactNode]\n  children: React.ReactNode\n}) {\n  const [isErrorOverlayOpen, setIsErrorOverlayOpen] = useState(false)\n  const openOverlay = useCallback(() => {\n    setIsErrorOverlayOpen(true)\n  }, [])\n\n  return (\n    <>\n      <AppDevOverlayErrorBoundary\n        globalError={globalError}\n        onError={setIsErrorOverlayOpen}\n      >\n        <ReplaySsrOnlyErrors onBlockingError={openOverlay} />\n        {children}\n      </AppDevOverlayErrorBoundary>\n      <>\n        {/* Fonts can only be loaded outside the Shadow DOM. */}\n        <FontStyles />\n        <DevOverlay\n          state={state}\n          isErrorOverlayOpen={isErrorOverlayOpen}\n          setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n        />\n      </>\n    </>\n  )\n}\n"], "names": ["AppDevOverlay", "readSsrError", "document", "ssrErrorTemplateTag", "querySelector", "message", "getAttribute", "stack", "digest", "error", "Error", "isNextRouterError", "ReplaySsrOnlyErrors", "onBlockingError", "process", "env", "NODE_ENV", "ssrError", "useEffect", "handleClientError", "MISSING_ROOT_TAGS_ERROR", "state", "globalError", "children", "isErrorOverlayOpen", "setIsErrorOverlayOpen", "useState", "openOverlay", "useCallback", "AppDevOverlayErrorBoundary", "onError", "FontStyles", "DevOverlay"], "mappings": ";;;;+BAsEgBA;;;eAAAA;;;;uBAnEiC;4CACN;4BAChB;4BACA;iCACO;mCACA;2BACM;AAExC,SAASC;IACP,IAAI,OAAOC,aAAa,aAAa;QACnC,OAAO;IACT;IAEA,MAAMC,sBAAsBD,SAASE,aAAa,CAChD;IAEF,IAAID,qBAAqB;QACvB,MAAME,UAAkBF,oBAAoBG,YAAY,CACtD;QAEF,MAAMC,QAAQJ,oBAAoBG,YAAY,CAAC;QAC/C,MAAME,SAASL,oBAAoBG,YAAY,CAAC;QAChD,MAAMG,QAAQ,qBAAkB,CAAlB,IAAIC,MAAML,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;QAC/B,IAAIG,QAAQ;;YACRC,MAAcD,MAAM,GAAGA;QAC3B;QACA,yFAAyF;QACzF,IAAIG,IAAAA,oCAAiB,EAACF,QAAQ;YAC5B,OAAO;QACT;QACAA,MAAMF,KAAK,GAAGA,SAAS;QACvB,OAAOE;IACT;IAEA,OAAO;AACT;AAEA,uDAAuD;AACvD,yDAAyD;AACzD,sFAAsF;AACtF,SAASG,oBAAoB,KAI5B;IAJ4B,IAAA,EAC3BC,eAAe,EAGhB,GAJ4B;IAK3B,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,wEAAwE;QACxE,MAAMC,WAAWhB;QACjB,sDAAsD;QACtDiB,IAAAA,gBAAS,EAAC;YACR,IAAID,aAAa,MAAM;gBACrB,yCAAyC;gBACzC,wCAAwC;gBACxC,4BAA4B;gBAC5BE,IAAAA,kCAAiB,EAACF;gBAElB,iEAAiE;gBACjE,IAAIA,SAAST,MAAM,KAAKY,kCAAuB,EAAE;oBAC/CP;gBACF;YACF;QACF,GAAG;YAACI;YAAUJ;SAAgB;IAChC;IAEA,OAAO;AACT;AAEO,SAASb,cAAc,KAQ7B;IAR6B,IAAA,EAC5BqB,KAAK,EACLC,WAAW,EACXC,QAAQ,EAKT,GAR6B;IAS5B,MAAM,CAACC,oBAAoBC,sBAAsB,GAAGC,IAAAA,eAAQ,EAAC;IAC7D,MAAMC,cAAcC,IAAAA,kBAAW,EAAC;QAC9BH,sBAAsB;IACxB,GAAG,EAAE;IAEL,qBACE;;0BACE,sBAACI,sDAA0B;gBACzBP,aAAaA;gBACbQ,SAASL;;kCAET,qBAACb;wBAAoBC,iBAAiBc;;oBACrCJ;;;0BAEH;;kCAEE,qBAACQ,sBAAU;kCACX,qBAACC,sBAAU;wBACTX,OAAOA;wBACPG,oBAAoBA;wBACpBC,uBAAuBA;;;;;;AAKjC"}