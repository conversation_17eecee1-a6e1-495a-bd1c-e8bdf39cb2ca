import { NextRequest, NextResponse } from 'next/server';
import { locales, defaultLocale, isValidLocale } from './lib/i18n';

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // تجاهل الملفات الثابتة والـ API routes والـ admin
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/uploads') ||
    pathname.startsWith('/admin') ||
    pathname.includes('.')
  ) {
    return NextResponse.next();
  }

  // تحقق من وجود locale في المسار
  const pathnameIsMissingLocale = locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  );

  // إذا كان المسار لا يحتوي على locale، أضف الـ locale المناسب
  if (pathnameIsMissingLocale) {
    // تحقق من تفضيل المستخدم في الكوكيز
    const preferredLocale = request.cookies.get('preferredLocale')?.value;

    // إذا لم يكن هناك تفضيل محفوظ، استخدم الافتراضي
    const targetLocale = preferredLocale && isValidLocale(preferredLocale)
      ? preferredLocale
      : defaultLocale;

    return NextResponse.redirect(
      new URL(`/${targetLocale}${pathname === '/' ? '' : pathname}`, request.url)
    );
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // تطبيق على جميع المسارات عدا:
    '/((?!api|_next/static|_next/image|favicon.ico|uploads).*)',
  ],
};
