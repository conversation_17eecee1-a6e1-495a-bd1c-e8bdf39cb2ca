{"version": 3, "sources": ["../../../src/shared/lib/head-manager-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\n\nexport const HeadManagerContext: React.Context<{\n  updateHead?: (state: any) => void\n  mountedInstances?: any\n  updateScripts?: (state: any) => void\n  scripts?: any\n  getIsSsr?: () => boolean\n\n  // Used in app directory, to render script tags as server components.\n  appDir?: boolean\n  nonce?: string\n}> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  HeadManagerContext.displayName = 'HeadManagerContext'\n}\n"], "names": ["React", "HeadManagerContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AAEzB,OAAO,MAAMC,qBAURD,MAAME,aAAa,CAAC,CAAC,GAAE;AAE5B,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCJ,mBAAmBK,WAAW,GAAG;AACnC"}