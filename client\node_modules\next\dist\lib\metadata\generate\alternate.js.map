{"version": 3, "sources": ["../../../../src/lib/metadata/generate/alternate.tsx"], "sourcesContent": ["import type { ResolvedMetadata } from '../types/metadata-interface'\nimport type { AlternateLinkDescriptor } from '../types/alternative-urls-types'\n\nimport React from 'react'\nimport { MetaFilter } from './meta'\n\nfunction AlternateLink({\n  descriptor,\n  ...props\n}: {\n  descriptor: AlternateLinkDescriptor\n} & React.LinkHTMLAttributes<HTMLLinkElement>) {\n  if (!descriptor.url) return null\n  return (\n    <link\n      {...props}\n      {...(descriptor.title && { title: descriptor.title })}\n      href={descriptor.url.toString()}\n    />\n  )\n}\n\nexport function AlternatesMetadata({\n  alternates,\n}: {\n  alternates: ResolvedMetadata['alternates']\n}) {\n  if (!alternates) return null\n\n  const { canonical, languages, media, types } = alternates\n\n  return MetaFilter([\n    canonical\n      ? AlternateLink({ rel: 'canonical', descriptor: canonical })\n      : null,\n    languages\n      ? Object.entries(languages).flatMap(([locale, descriptors]) =>\n          descriptors?.map((descriptor) =>\n            AlternateLink({ rel: 'alternate', hrefLang: locale, descriptor })\n          )\n        )\n      : null,\n    media\n      ? Object.entries(media).flatMap(([mediaName, descriptors]) =>\n          descriptors?.map((descriptor) =>\n            AlternateLink({ rel: 'alternate', media: mediaName, descriptor })\n          )\n        )\n      : null,\n    types\n      ? Object.entries(types).flatMap(([type, descriptors]) =>\n          descriptors?.map((descriptor) =>\n            AlternateLink({ rel: 'alternate', type, descriptor })\n          )\n        )\n      : null,\n  ])\n}\n"], "names": ["AlternatesMetadata", "AlternateLink", "descriptor", "props", "url", "link", "title", "href", "toString", "alternates", "canonical", "languages", "media", "types", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rel", "Object", "entries", "flatMap", "locale", "descriptors", "map", "hrefLang", "mediaName", "type"], "mappings": ";;;;+BAsBgBA;;;eAAAA;;;;8DAnBE;sBACS;;;;;;AAE3B,SAASC,cAAc,EACrBC,UAAU,EACV,GAAGC,OAGwC;IAC3C,IAAI,CAACD,WAAWE,GAAG,EAAE,OAAO;IAC5B,qBACE,qBAACC;QACE,GAAGF,KAAK;QACR,GAAID,WAAWI,KAAK,IAAI;YAAEA,OAAOJ,WAAWI,KAAK;QAAC,CAAC;QACpDC,MAAML,WAAWE,GAAG,CAACI,QAAQ;;AAGnC;AAEO,SAASR,mBAAmB,EACjCS,UAAU,EAGX;IACC,IAAI,CAACA,YAAY,OAAO;IAExB,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,EAAE,GAAGJ;IAE/C,OAAOK,IAAAA,gBAAU,EAAC;QAChBJ,YACIT,cAAc;YAAEc,KAAK;YAAab,YAAYQ;QAAU,KACxD;QACJC,YACIK,OAAOC,OAAO,CAACN,WAAWO,OAAO,CAAC,CAAC,CAACC,QAAQC,YAAY,GACtDA,+BAAAA,YAAaC,GAAG,CAAC,CAACnB,aAChBD,cAAc;oBAAEc,KAAK;oBAAaO,UAAUH;oBAAQjB;gBAAW,OAGnE;QACJU,QACII,OAAOC,OAAO,CAACL,OAAOM,OAAO,CAAC,CAAC,CAACK,WAAWH,YAAY,GACrDA,+BAAAA,YAAaC,GAAG,CAAC,CAACnB,aAChBD,cAAc;oBAAEc,KAAK;oBAAaH,OAAOW;oBAAWrB;gBAAW,OAGnE;QACJW,QACIG,OAAOC,OAAO,CAACJ,OAAOK,OAAO,CAAC,CAAC,CAACM,MAAMJ,YAAY,GAChDA,+BAAAA,YAAaC,GAAG,CAAC,CAACnB,aAChBD,cAAc;oBAAEc,KAAK;oBAAaS;oBAAMtB;gBAAW,OAGvD;KACL;AACH"}