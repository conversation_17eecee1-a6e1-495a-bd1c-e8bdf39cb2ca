import { NextApiRequest, NextApiResponse } from 'next';
import { getSubcategoryById, updateSubcategory, deleteSubcategory } from '../../../utils/database';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  if (typeof id !== 'string') {
    return res.status(400).json({ error: 'Invalid subcategory ID' });
  }

  try {
    switch (req.method) {
      case 'GET':
        const subcategory = getSubcategoryById(id);
        if (!subcategory) {
          return res.status(404).json({ error: 'Subcategory not found' });
        }
        res.status(200).json(subcategory);
        break;

      case 'PUT':
        const updates = req.body;
        const updatedSubcategory = updateSubcategory(id, updates);
        if (!updatedSubcategory) {
          return res.status(404).json({ error: 'Subcategory not found' });
        }
        res.status(200).json(updatedSubcategory);
        break;

      case 'DELETE':
        const deleted = deleteSubcategory(id);
        if (!deleted) {
          return res.status(404).json({ error: 'Subcategory not found' });
        }
        res.status(200).json({ message: 'Subcategory deleted successfully' });
        break;

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
