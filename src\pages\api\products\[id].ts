import { NextApiRequest, NextApiResponse } from 'next';
import { getProductById, updateProduct, deleteProduct } from '../../../utils/database';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query;

  if (typeof id !== 'string') {
    return res.status(400).json({ error: 'Invalid product ID' });
  }

  try {
    switch (req.method) {
      case 'GET':
        const product = getProductById(id);
        if (!product) {
          return res.status(404).json({ error: 'Product not found' });
        }
        res.status(200).json(product);
        break;

      case 'PUT':
        const updates = req.body;
        const updatedProduct = updateProduct(id, updates);
        if (!updatedProduct) {
          return res.status(404).json({ error: 'Product not found' });
        }
        res.status(200).json(updatedProduct);
        break;

      case 'DELETE':
        const deleted = deleteProduct(id);
        if (!deleted) {
          return res.status(404).json({ error: 'Product not found' });
        }
        res.status(200).json({ message: 'Product deleted successfully' });
        break;

      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
