{"version": 3, "sources": ["../../src/server/get-route-from-entrypoint.ts"], "sourcesContent": ["import getAppRouteFromEntrypoint from './get-app-route-from-entrypoint'\nimport matchBundle from './match-bundle'\n\n// matches pages/:page*.js\nconst SERVER_ROUTE_NAME_REGEX = /^pages[/\\\\](.*)$/\n\n// matches static/pages/:page*.js\nconst BROWSER_ROUTE_NAME_REGEX = /^static[/\\\\]pages[/\\\\](.*)$/\n\nexport default function getRouteFromEntrypoint(\n  entryFile: string,\n  app?: boolean\n): string | null {\n  let pagePath = matchBundle(SERVER_ROUTE_NAME_REGEX, entryFile)\n\n  if (pagePath) {\n    return pagePath\n  }\n\n  if (app) {\n    pagePath = getAppRouteFromEntrypoint(entryFile)\n    if (pagePath) return pagePath\n  }\n\n  // Potentially the passed item is a browser bundle so we try to match that also\n  return matchBundle(BROWSER_ROUTE_NAME_REGEX, entryFile)\n}\n"], "names": ["getAppRouteFromEntrypoint", "matchBundle", "SERVER_ROUTE_NAME_REGEX", "BROWSER_ROUTE_NAME_REGEX", "getRouteFromEntrypoint", "entryFile", "app", "pagePath"], "mappings": "AAAA,OAAOA,+BAA+B,kCAAiC;AACvE,OAAOC,iBAAiB,iBAAgB;AAExC,0BAA0B;AAC1B,MAAMC,0BAA0B;AAEhC,iCAAiC;AACjC,MAAMC,2BAA2B;AAEjC,eAAe,SAASC,uBACtBC,SAAiB,EACjBC,GAAa;IAEb,IAAIC,WAAWN,YAAYC,yBAAyBG;IAEpD,IAAIE,UAAU;QACZ,OAAOA;IACT;IAEA,IAAID,KAAK;QACPC,WAAWP,0BAA0BK;QACrC,IAAIE,UAAU,OAAOA;IACvB;IAEA,+EAA+E;IAC/E,OAAON,YAAYE,0BAA0BE;AAC/C"}