'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Locale } from '../../../lib/i18n';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: any[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}

interface CompanySettings {
  email: string;
  whatsapp: string;
}

export default function DashboardPage() {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;

  const [quoteRequests, setQuoteRequests] = useState<QuoteRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [companySettings, setCompanySettings] = useState<CompanySettings>({
    email: '',
    whatsapp: ''
  });

  const content = {
    ar: {
      title: 'لوحة التحكم',
      subtitle: 'إدارة طلبات التسعير',
      quoteRequests: 'طلبات التسعير',
      noRequests: 'لا توجد طلبات تسعير',
      customerName: 'اسم العميل',
      phone: 'رقم الهاتف',
      email: 'البريد الإلكتروني',
      company: 'الشركة',
      status: 'الحالة',
      actions: 'الإجراءات',
      openExcel: 'فتح Excel',
      send: 'إرسال',
      whatsapp: 'واتساب',
      settings: 'الإعدادات',
      companyEmail: 'إيميل الشركة',
      companyWhatsapp: 'رقم واتساب الشركة',
      save: 'حفظ',
      cancel: 'إلغاء',
      pending: 'في الانتظار',
      processed: 'تم المعالجة',
      sent: 'تم الإرسال',
      createdAt: 'تاريخ الطلب',
      productsCount: 'عدد المنتجات'
    },
    en: {
      title: 'Dashboard',
      subtitle: 'Quote Requests Management',
      quoteRequests: 'Quote Requests',
      noRequests: 'No quote requests',
      customerName: 'Customer Name',
      phone: 'Phone',
      email: 'Email',
      company: 'Company',
      status: 'Status',
      actions: 'Actions',
      openExcel: 'Open Excel',
      send: 'Send',
      whatsapp: 'WhatsApp',
      settings: 'Settings',
      companyEmail: 'Company Email',
      companyWhatsapp: 'Company WhatsApp',
      save: 'Save',
      cancel: 'Cancel',
      pending: 'Pending',
      processed: 'Processed',
      sent: 'Sent',
      createdAt: 'Created At',
      productsCount: 'Products Count'
    }
  };

  const currentContent = content[locale];

  useEffect(() => {
    fetchQuoteRequests();
    loadCompanySettings();
  }, []);

  const fetchQuoteRequests = async () => {
    try {
      const response = await fetch('/api/quote-requests');
      const result = await response.json();
      
      if (result.success) {
        setQuoteRequests(result.requests);
      }
    } catch (error) {
      console.error('Error fetching quote requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCompanySettings = () => {
    const savedSettings = localStorage.getItem('companySettings');
    if (savedSettings) {
      setCompanySettings(JSON.parse(savedSettings));
    }
  };

  const saveCompanySettings = () => {
    localStorage.setItem('companySettings', JSON.stringify(companySettings));
    setShowSettings(false);
  };

  const handleDownloadExcel = (excelFilePath: string) => {
    const filename = excelFilePath.split('/').pop();
    window.open(`/api/download-excel/${filename}`, '_blank');
  };

  const handleSendEmail = (request: QuoteRequest) => {
    const subject = `عرض سعر - ${request.id}`;
    const body = `مرحباً ${request.customerInfo.name}،\n\nنشكركم على طلب التسعير. يرجى مراجعة العرض المرفق.\n\nمع تحياتنا،\nفريق دروب هاجر`;
    
    window.open(`mailto:${request.customerInfo.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
  };

  const handleWhatsApp = (request: QuoteRequest) => {
    // قراءة رسالة الواتساب من الإعدادات
    let message = `مرحباً ${request.customerInfo.name}، نشكركم على طلب التسعير رقم ${request.id}. سنتواصل معكم قريباً بالعرض المطلوب.`;

    try {
      const savedSettings = localStorage.getItem('siteSettings');
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        if (settings.communicationSettings?.whatsapp?.quoteResponseMessageAr) {
          message = `مرحباً ${request.customerInfo.name}، ${settings.communicationSettings.whatsapp.quoteResponseMessageAr}`;
        }
      }
    } catch (error) {
      console.log('Using default WhatsApp message');
    }

    const phoneNumber = request.customerInfo.phone.replace(/[^0-9]/g, '');
    window.open(`https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`);
  };

  const updateRequestStatus = async (requestId: string, status: string) => {
    try {
      const response = await fetch(`/api/quote-requests/${requestId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (response.ok) {
        fetchQuoteRequests(); // إعادة تحميل البيانات
      }
    } catch (error) {
      console.error('Error updating request status:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(locale === 'ar' ? 'ar-SA' : 'en-US');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processed': return 'bg-blue-100 text-blue-800';
      case 'sent': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <>
        <Navbar locale={locale} />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">جاري التحميل...</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar locale={locale} />
      <main className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">
                {currentContent.title}
              </h1>
              <p className="text-gray-600">
                {currentContent.subtitle}
              </p>
            </div>
            
            <button
              onClick={() => setShowSettings(true)}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2"
            >
              <i className="ri-settings-line"></i>
              {currentContent.settings}
            </button>
          </div>

          {/* Quote Requests Table */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-bold text-gray-800">
                {currentContent.quoteRequests} ({quoteRequests.length})
              </h2>
            </div>

            {quoteRequests.length === 0 ? (
              <div className="p-8 text-center">
                <i className="ri-file-list-line text-4xl text-gray-400 mb-4"></i>
                <p className="text-gray-600">{currentContent.noRequests}</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {currentContent.customerName}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {currentContent.phone}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {currentContent.email}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {currentContent.company}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {currentContent.productsCount}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {currentContent.status}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {currentContent.createdAt}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {currentContent.actions}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {quoteRequests.map((request) => (
                      <tr key={request.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="font-medium text-gray-900">
                            {request.customerInfo.name}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-gray-600">
                          {request.customerInfo.phone}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-gray-600">
                          {request.customerInfo.email}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-gray-600">
                          {request.customerInfo.company || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-gray-600">
                          {request.products.length}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(request.status)}`}>
                            {currentContent[request.status as keyof typeof currentContent]}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-gray-600">
                          {formatDate(request.createdAt)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex gap-2">
                            <button
                              onClick={() => handleDownloadExcel(request.excelFilePath)}
                              className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors"
                              title={currentContent.openExcel}
                            >
                              <i className="ri-file-excel-line"></i>
                            </button>
                            <button
                              onClick={() => handleSendEmail(request)}
                              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors"
                              title={currentContent.send}
                            >
                              <i className="ri-mail-line"></i>
                            </button>
                            <button
                              onClick={() => handleWhatsApp(request)}
                              className="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600 transition-colors"
                              title={currentContent.whatsapp}
                            >
                              <i className="ri-whatsapp-line"></i>
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Settings Modal */}
        {showSettings && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl p-6 w-full max-w-md">
              <h3 className="text-xl font-bold text-gray-800 mb-4">
                {currentContent.settings}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-700 font-medium mb-2">
                    {currentContent.companyEmail}
                  </label>
                  <input
                    type="email"
                    value={companySettings.email}
                    onChange={(e) => setCompanySettings(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label className="block text-gray-700 font-medium mb-2">
                    {currentContent.companyWhatsapp}
                  </label>
                  <input
                    type="tel"
                    value={companySettings.whatsapp}
                    onChange={(e) => setCompanySettings(prev => ({ ...prev, whatsapp: e.target.value }))}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="966xxxxxxxxx"
                  />
                </div>
              </div>
              
              <div className="flex gap-3 mt-6">
                <button
                  onClick={saveCompanySettings}
                  className="flex-1 bg-primary text-white py-2 px-4 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
                >
                  {currentContent.save}
                </button>
                <button
                  onClick={() => setShowSettings(false)}
                  className="flex-1 bg-gray-200 text-gray-700 py-2 px-4 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
                >
                  {currentContent.cancel}
                </button>
              </div>
            </div>
          </div>
        )}
      </main>
      <Footer locale={locale} />
    </>
  );
}
