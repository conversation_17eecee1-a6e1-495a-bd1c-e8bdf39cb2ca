{"version": 3, "sources": ["../../src/export/index.ts"], "sourcesContent": ["import type {\n  ExportAppResult,\n  ExportAppOptions,\n  WorkerRenderOptsPartial,\n} from './types'\nimport { createStaticWorker, type PrerenderManifest } from '../build'\nimport type { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\n\nimport { bold, yellow } from '../lib/picocolors'\nimport findUp from 'next/dist/compiled/find-up'\nimport { existsSync, promises as fs } from 'fs'\n\nimport '../server/require-hook'\n\nimport { dirname, join, resolve, sep, relative } from 'path'\nimport { formatAmpMessages } from '../build/output/index'\nimport type { AmpPageStatus } from '../build/output/index'\nimport * as Log from '../build/output/log'\nimport {\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n  RSC_SUFFIX,\n  SSG_FALLBACK_EXPORT_ERROR,\n} from '../lib/constants'\nimport { recursiveCopy } from '../lib/recursive-copy'\nimport {\n  BUILD_ID_FILE,\n  CLIENT_PUBLIC_FILES_PATH,\n  CLIENT_STATIC_FILES_PATH,\n  EXPORT_DETAIL,\n  EXPORT_MARKER,\n  NEXT_FONT_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  PAGES_MANIFEST,\n  PHASE_EXPORT,\n  PRERENDER_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVER_REFERENCE_MANIFEST,\n  APP_PATH_ROUTES_MANIFEST,\n  ROUTES_MANIFEST,\n  FUNCTIONS_CONFIG_MANIFEST,\n} from '../shared/lib/constants'\nimport loadConfig from '../server/config'\nimport type { ExportPathMap } from '../server/config-shared'\nimport { eventCliSession } from '../telemetry/events'\nimport { hasNextSupport } from '../server/ci-info'\nimport { Telemetry } from '../telemetry/storage'\nimport { normalizePagePath } from '../shared/lib/page-path/normalize-page-path'\nimport { denormalizePagePath } from '../shared/lib/page-path/denormalize-page-path'\nimport { loadEnvConfig } from '@next/env'\nimport { isAPIRoute } from '../lib/is-api-route'\nimport { getPagePath } from '../server/require'\nimport type { Span } from '../trace'\nimport type { MiddlewareManifest } from '../build/webpack/plugins/middleware-plugin'\nimport { isAppRouteRoute } from '../lib/is-app-route-route'\nimport { isAppPageRoute } from '../lib/is-app-page-route'\nimport isError from '../lib/is-error'\nimport { formatManifest } from '../build/manifests/formatter/format-manifest'\nimport { TurborepoAccessTraceResult } from '../build/turborepo-access-trace'\nimport { createProgress } from '../build/progress'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { isInterceptionRouteRewrite } from '../lib/generate-interception-routes-rewrites'\nimport type { ActionManifest } from '../build/webpack/plugins/flight-client-entry-plugin'\nimport { extractInfoFromServerReferenceId } from '../shared/lib/server-reference-info'\nimport { convertSegmentPathToStaticExportFilename } from '../shared/lib/segment-cache/segment-value-encoding'\n\nexport class ExportError extends Error {\n  code = 'NEXT_EXPORT_ERROR'\n}\n\nasync function exportAppImpl(\n  dir: string,\n  options: Readonly<ExportAppOptions>,\n  span: Span\n): Promise<ExportAppResult | null> {\n  dir = resolve(dir)\n\n  // attempt to load global env values so they are available in next.config.js\n  span.traceChild('load-dotenv').traceFn(() => loadEnvConfig(dir, false, Log))\n\n  const { enabledDirectories } = options\n\n  const nextConfig =\n    options.nextConfig ||\n    (await span\n      .traceChild('load-next-config')\n      .traceAsyncFn(() => loadConfig(PHASE_EXPORT, dir)))\n\n  const distDir = join(dir, nextConfig.distDir)\n  const telemetry = options.buildExport ? null : new Telemetry({ distDir })\n\n  if (telemetry) {\n    telemetry.record(\n      eventCliSession(distDir, nextConfig, {\n        webpackVersion: null,\n        cliCommand: 'export',\n        isSrcDir: null,\n        hasNowJson: !!(await findUp('now.json', { cwd: dir })),\n        isCustomServer: null,\n        turboFlag: false,\n        pagesDir: null,\n        appDir: null,\n      })\n    )\n  }\n\n  const subFolders = nextConfig.trailingSlash && !options.buildExport\n\n  if (!options.silent && !options.buildExport) {\n    Log.info(`using build directory: ${distDir}`)\n  }\n\n  const buildIdFile = join(distDir, BUILD_ID_FILE)\n\n  if (!existsSync(buildIdFile)) {\n    throw new ExportError(\n      `Could not find a production build in the '${distDir}' directory. Try building your app with 'next build' before starting the static export. https://nextjs.org/docs/messages/next-export-no-build-id`\n    )\n  }\n\n  const customRoutes = ['rewrites', 'redirects', 'headers'].filter(\n    (config) => typeof nextConfig[config] === 'function'\n  )\n\n  if (!hasNextSupport && !options.buildExport && customRoutes.length > 0) {\n    Log.warn(\n      `rewrites, redirects, and headers are not applied when exporting your application, detected (${customRoutes.join(\n        ', '\n      )}). See more info here: https://nextjs.org/docs/messages/export-no-custom-routes`\n    )\n  }\n\n  const buildId = await fs.readFile(buildIdFile, 'utf8')\n\n  const pagesManifest =\n    !options.pages &&\n    (require(join(distDir, SERVER_DIRECTORY, PAGES_MANIFEST)) as PagesManifest)\n\n  let prerenderManifest: DeepReadonly<PrerenderManifest> | undefined\n  try {\n    prerenderManifest = require(join(distDir, PRERENDER_MANIFEST))\n  } catch {}\n\n  let appRoutePathManifest: Record<string, string> | undefined\n  try {\n    appRoutePathManifest = require(join(distDir, APP_PATH_ROUTES_MANIFEST))\n  } catch (err) {\n    if (\n      isError(err) &&\n      (err.code === 'ENOENT' || err.code === 'MODULE_NOT_FOUND')\n    ) {\n      // the manifest doesn't exist which will happen when using\n      // \"pages\" dir instead of \"app\" dir.\n      appRoutePathManifest = undefined\n    } else {\n      // the manifest is malformed (invalid json)\n      throw err\n    }\n  }\n\n  const excludedPrerenderRoutes = new Set<string>()\n  const pages = options.pages || Object.keys(pagesManifest)\n  const defaultPathMap: ExportPathMap = {}\n\n  let hasApiRoutes = false\n  for (const page of pages) {\n    // _document and _app are not real pages\n    // _error is exported as 404.html later on\n    // API Routes are Node.js functions\n\n    if (isAPIRoute(page)) {\n      hasApiRoutes = true\n      continue\n    }\n\n    if (page === '/_document' || page === '/_app' || page === '/_error') {\n      continue\n    }\n\n    // iSSG pages that are dynamic should not export templated version by\n    // default. In most cases, this would never work. There is no server that\n    // could run `getStaticProps`. If users make their page work lazily, they\n    // can manually add it to the `exportPathMap`.\n    if (prerenderManifest?.dynamicRoutes[page]) {\n      excludedPrerenderRoutes.add(page)\n      continue\n    }\n\n    defaultPathMap[page] = { page }\n  }\n\n  const mapAppRouteToPage = new Map<string, string>()\n  if (!options.buildExport && appRoutePathManifest) {\n    for (const [pageName, routePath] of Object.entries(appRoutePathManifest)) {\n      mapAppRouteToPage.set(routePath, pageName)\n      if (\n        isAppPageRoute(pageName) &&\n        !prerenderManifest?.routes[routePath] &&\n        !prerenderManifest?.dynamicRoutes[routePath]\n      ) {\n        defaultPathMap[routePath] = {\n          page: pageName,\n          _isAppDir: true,\n        }\n      }\n    }\n  }\n\n  // Initialize the output directory\n  const outDir = options.outdir\n\n  if (outDir === join(dir, 'public')) {\n    throw new ExportError(\n      `The 'public' directory is reserved in Next.js and can not be used as the export out directory. https://nextjs.org/docs/messages/can-not-output-to-public`\n    )\n  }\n\n  if (outDir === join(dir, 'static')) {\n    throw new ExportError(\n      `The 'static' directory is reserved in Next.js and can not be used as the export out directory. https://nextjs.org/docs/messages/can-not-output-to-static`\n    )\n  }\n\n  await fs.rm(outDir, { recursive: true, force: true })\n  await fs.mkdir(join(outDir, '_next', buildId), { recursive: true })\n\n  await fs.writeFile(\n    join(distDir, EXPORT_DETAIL),\n    formatManifest({\n      version: 1,\n      outDirectory: outDir,\n      success: false,\n    }),\n    'utf8'\n  )\n\n  // Copy static directory\n  if (!options.buildExport && existsSync(join(dir, 'static'))) {\n    if (!options.silent) {\n      Log.info('Copying \"static\" directory')\n    }\n    await span\n      .traceChild('copy-static-directory')\n      .traceAsyncFn(() =>\n        recursiveCopy(join(dir, 'static'), join(outDir, 'static'))\n      )\n  }\n\n  // Copy .next/static directory\n  if (\n    !options.buildExport &&\n    existsSync(join(distDir, CLIENT_STATIC_FILES_PATH))\n  ) {\n    if (!options.silent) {\n      Log.info('Copying \"static build\" directory')\n    }\n    await span\n      .traceChild('copy-next-static-directory')\n      .traceAsyncFn(() =>\n        recursiveCopy(\n          join(distDir, CLIENT_STATIC_FILES_PATH),\n          join(outDir, '_next', CLIENT_STATIC_FILES_PATH)\n        )\n      )\n  }\n\n  // Get the exportPathMap from the config file\n  if (typeof nextConfig.exportPathMap !== 'function') {\n    nextConfig.exportPathMap = async (defaultMap) => {\n      return defaultMap\n    }\n  }\n\n  const {\n    i18n,\n    images: { loader = 'default', unoptimized },\n  } = nextConfig\n\n  if (i18n && !options.buildExport) {\n    throw new ExportError(\n      `i18n support is not compatible with next export. See here for more info on deploying: https://nextjs.org/docs/messages/export-no-custom-routes`\n    )\n  }\n\n  if (!options.buildExport) {\n    const { isNextImageImported } = await span\n      .traceChild('is-next-image-imported')\n      .traceAsyncFn(() =>\n        fs\n          .readFile(join(distDir, EXPORT_MARKER), 'utf8')\n          .then((text) => JSON.parse(text))\n          .catch(() => ({}))\n      )\n\n    if (\n      isNextImageImported &&\n      loader === 'default' &&\n      !unoptimized &&\n      !hasNextSupport\n    ) {\n      throw new ExportError(\n        `Image Optimization using the default loader is not compatible with export.\n  Possible solutions:\n    - Use \\`next start\\` to run a server, which includes the Image Optimization API.\n    - Configure \\`images.unoptimized = true\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n  }\n\n  let serverActionsManifest: ActionManifest | undefined\n  if (enabledDirectories.app) {\n    serverActionsManifest = require(\n      join(distDir, SERVER_DIRECTORY, SERVER_REFERENCE_MANIFEST + '.json')\n    ) as ActionManifest\n\n    if (nextConfig.output === 'export') {\n      const routesManifest = require(join(distDir, ROUTES_MANIFEST))\n\n      // We already prevent rewrites earlier in the process, however Next.js will insert rewrites\n      // for interception routes so we need to check for that here.\n      if (routesManifest?.rewrites?.beforeFiles?.length > 0) {\n        const hasInterceptionRouteRewrite =\n          routesManifest.rewrites.beforeFiles.some(isInterceptionRouteRewrite)\n\n        if (hasInterceptionRouteRewrite) {\n          throw new ExportError(\n            `Intercepting routes are not supported with static export.\\nRead more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports#unsupported-features`\n          )\n        }\n      }\n\n      const actionIds = [\n        ...Object.keys(serverActionsManifest.node),\n        ...Object.keys(serverActionsManifest.edge),\n      ]\n\n      if (\n        actionIds.some(\n          (actionId) =>\n            extractInfoFromServerReferenceId(actionId).type === 'server-action'\n        )\n      ) {\n        throw new ExportError(\n          `Server Actions are not supported with static export.\\nRead more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports#unsupported-features`\n        )\n      }\n    }\n  }\n\n  // Start the rendering process\n  const renderOpts: WorkerRenderOptsPartial = {\n    previewProps: prerenderManifest?.preview,\n    nextExport: true,\n    assetPrefix: nextConfig.assetPrefix.replace(/\\/$/, ''),\n    distDir,\n    dev: false,\n    basePath: nextConfig.basePath,\n    trailingSlash: nextConfig.trailingSlash,\n    canonicalBase: nextConfig.amp?.canonicalBase || '',\n    ampSkipValidation: nextConfig.experimental.amp?.skipValidation || false,\n    ampOptimizerConfig: nextConfig.experimental.amp?.optimizer || undefined,\n    locales: i18n?.locales,\n    locale: i18n?.defaultLocale,\n    defaultLocale: i18n?.defaultLocale,\n    domainLocales: i18n?.domains,\n    disableOptimizedLoading: nextConfig.experimental.disableOptimizedLoading,\n    // Exported pages do not currently support dynamic HTML.\n    supportsDynamicResponse: false,\n    crossOrigin: nextConfig.crossOrigin,\n    optimizeCss: nextConfig.experimental.optimizeCss,\n    nextConfigOutput: nextConfig.output,\n    nextScriptWorkers: nextConfig.experimental.nextScriptWorkers,\n    largePageDataBytes: nextConfig.experimental.largePageDataBytes,\n    serverActions: nextConfig.experimental.serverActions,\n    serverComponents: enabledDirectories.app,\n    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n    nextFontManifest: require(\n      join(distDir, 'server', `${NEXT_FONT_MANIFEST}.json`)\n    ),\n    images: nextConfig.images,\n    ...(enabledDirectories.app\n      ? {\n          serverActionsManifest,\n        }\n      : {}),\n    strictNextHead: nextConfig.experimental.strictNextHead ?? true,\n    deploymentId: nextConfig.deploymentId,\n    htmlLimitedBots: nextConfig.htmlLimitedBots.source,\n    experimental: {\n      clientTraceMetadata: nextConfig.experimental.clientTraceMetadata,\n      expireTime: nextConfig.expireTime,\n      dynamicIO: nextConfig.experimental.dynamicIO ?? false,\n      clientSegmentCache:\n        nextConfig.experimental.clientSegmentCache === 'client-only'\n          ? 'client-only'\n          : Boolean(nextConfig.experimental.clientSegmentCache),\n      dynamicOnHover: nextConfig.experimental.dynamicOnHover ?? false,\n      inlineCss: nextConfig.experimental.inlineCss ?? false,\n      authInterrupts: !!nextConfig.experimental.authInterrupts,\n    },\n    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n  }\n\n  const { publicRuntimeConfig } = nextConfig\n\n  if (Object.keys(publicRuntimeConfig).length > 0) {\n    renderOpts.runtimeConfig = publicRuntimeConfig\n  }\n\n  // We need this for server rendering the Link component.\n  ;(globalThis as any).__NEXT_DATA__ = {\n    nextExport: true,\n  }\n\n  const exportPathMap = await span\n    .traceChild('run-export-path-map')\n    .traceAsyncFn(async () => {\n      const exportMap = await nextConfig.exportPathMap(defaultPathMap, {\n        dev: false,\n        dir,\n        outDir,\n        distDir,\n        buildId,\n      })\n      return exportMap\n    })\n\n  // only add missing 404 page when `buildExport` is false\n  if (!options.buildExport) {\n    // only add missing /404 if not specified in `exportPathMap`\n    if (!exportPathMap['/404']) {\n      exportPathMap['/404'] = { page: '/_error' }\n    }\n\n    /**\n     * exports 404.html for backwards compat\n     * E.g. GitHub Pages, GitLab Pages, Cloudflare Pages, Netlify\n     */\n    if (!exportPathMap['/404.html']) {\n      // alias /404.html to /404 to be compatible with custom 404 / _error page\n      exportPathMap['/404.html'] = exportPathMap['/404']\n    }\n  }\n\n  // make sure to prevent duplicates\n  const exportPaths = [\n    ...new Set(\n      Object.keys(exportPathMap).map((path) =>\n        denormalizePagePath(normalizePagePath(path))\n      )\n    ),\n  ]\n\n  const filteredPaths = exportPaths.filter(\n    (route) =>\n      exportPathMap[route]._isAppDir ||\n      // Remove API routes\n      !isAPIRoute(exportPathMap[route].page)\n  )\n\n  if (filteredPaths.length !== exportPaths.length) {\n    hasApiRoutes = true\n  }\n\n  if (filteredPaths.length === 0) {\n    return null\n  }\n\n  if (prerenderManifest && !options.buildExport) {\n    const fallbackEnabledPages = new Set()\n\n    for (const path of Object.keys(exportPathMap)) {\n      const page = exportPathMap[path].page\n      const prerenderInfo = prerenderManifest.dynamicRoutes[page]\n\n      if (prerenderInfo && prerenderInfo.fallback !== false) {\n        fallbackEnabledPages.add(page)\n      }\n    }\n\n    if (fallbackEnabledPages.size > 0) {\n      throw new ExportError(\n        `Found pages with \\`fallback\\` enabled:\\n${[\n          ...fallbackEnabledPages,\n        ].join('\\n')}\\n${SSG_FALLBACK_EXPORT_ERROR}\\n`\n      )\n    }\n  }\n  let hasMiddleware = false\n\n  if (!options.buildExport) {\n    try {\n      const middlewareManifest = require(\n        join(distDir, SERVER_DIRECTORY, MIDDLEWARE_MANIFEST)\n      ) as MiddlewareManifest\n\n      const functionsConfigManifest = require(\n        join(distDir, SERVER_DIRECTORY, FUNCTIONS_CONFIG_MANIFEST)\n      )\n\n      hasMiddleware =\n        Object.keys(middlewareManifest.middleware).length > 0 ||\n        Boolean(functionsConfigManifest.functions?.['/_middleware'])\n    } catch {}\n\n    // Warn if the user defines a path for an API page\n    if (hasApiRoutes || hasMiddleware) {\n      if (nextConfig.output === 'export') {\n        Log.warn(\n          yellow(\n            `Statically exporting a Next.js application via \\`next export\\` disables API routes and middleware.`\n          ) +\n            `\\n` +\n            yellow(\n              `This command is meant for static-only hosts, and is` +\n                ' ' +\n                bold(`not necessary to make your application static.`)\n            ) +\n            `\\n` +\n            yellow(\n              `Pages in your application without server-side data dependencies will be automatically statically exported by \\`next build\\`, including pages powered by \\`getStaticProps\\`.`\n            ) +\n            `\\n` +\n            yellow(\n              `Learn more: https://nextjs.org/docs/messages/api-routes-static-export`\n            )\n        )\n      }\n    }\n  }\n\n  const pagesDataDir = options.buildExport\n    ? outDir\n    : join(outDir, '_next/data', buildId)\n\n  const ampValidations: AmpPageStatus = {}\n\n  const publicDir = join(dir, CLIENT_PUBLIC_FILES_PATH)\n  // Copy public directory\n  if (!options.buildExport && existsSync(publicDir)) {\n    if (!options.silent) {\n      Log.info('Copying \"public\" directory')\n    }\n    await span.traceChild('copy-public-directory').traceAsyncFn(() =>\n      recursiveCopy(publicDir, outDir, {\n        filter(path) {\n          // Exclude paths used by pages\n          return !exportPathMap[path]\n        },\n      })\n    )\n  }\n\n  const failedExportAttemptsByPage: Map<string, boolean> = new Map()\n\n  // Chunk filtered pages into smaller groups, and call the export worker on each group.\n  // We've set a default minimum of 25 pages per chunk to ensure that even setups\n  // with only a few static pages can leverage a shared incremental cache, however this\n  // value can be configured.\n  const minChunkSize =\n    nextConfig.experimental.staticGenerationMinPagesPerWorker ?? 25\n  // Calculate the number of workers needed to ensure each chunk has at least minChunkSize pages\n  const numWorkers = Math.min(\n    options.numWorkers,\n    Math.ceil(filteredPaths.length / minChunkSize)\n  )\n  // Calculate the chunk size based on the number of workers\n  const chunkSize = Math.ceil(filteredPaths.length / numWorkers)\n  const chunks = Array.from({ length: numWorkers }, (_, i) =>\n    filteredPaths.slice(i * chunkSize, (i + 1) * chunkSize)\n  )\n  // Distribute remaining pages\n  const remainingPages = filteredPaths.slice(numWorkers * chunkSize)\n  remainingPages.forEach((page, index) => {\n    chunks[index % chunks.length].push(page)\n  })\n\n  const progress = createProgress(\n    filteredPaths.length,\n    options.statusMessage || 'Exporting'\n  )\n\n  const worker = createStaticWorker(nextConfig, progress)\n\n  const results = (\n    await Promise.all(\n      chunks.map((paths) =>\n        worker.exportPages({\n          buildId,\n          paths,\n          exportPathMap,\n          parentSpanId: span.getId(),\n          pagesDataDir,\n          renderOpts,\n          options,\n          dir,\n          distDir,\n          outDir,\n          nextConfig,\n          cacheHandler: nextConfig.cacheHandler,\n          cacheMaxMemorySize: nextConfig.cacheMaxMemorySize,\n          fetchCache: true,\n          fetchCacheKeyPrefix: nextConfig.experimental.fetchCacheKeyPrefix,\n        })\n      )\n    )\n  ).flat()\n\n  let hadValidationError = false\n\n  const collector: ExportAppResult = {\n    byPath: new Map(),\n    byPage: new Map(),\n    ssgNotFoundPaths: new Set(),\n    turborepoAccessTraceResults: new Map(),\n  }\n\n  for (const { result, path, pageKey } of results) {\n    if (!result) continue\n    if ('error' in result) {\n      failedExportAttemptsByPage.set(pageKey, true)\n      continue\n    }\n\n    const { page } = exportPathMap[path]\n\n    if (result.turborepoAccessTraceResult) {\n      collector.turborepoAccessTraceResults?.set(\n        path,\n        TurborepoAccessTraceResult.fromSerialized(\n          result.turborepoAccessTraceResult\n        )\n      )\n    }\n\n    // Capture any amp validations.\n    if (result.ampValidations) {\n      for (const validation of result.ampValidations) {\n        ampValidations[validation.page] = validation.result\n        hadValidationError ||= validation.result.errors.length > 0\n      }\n    }\n\n    if (options.buildExport) {\n      // Update path info by path.\n      const info = collector.byPath.get(path) ?? {}\n      if (result.cacheControl) {\n        info.cacheControl = result.cacheControl\n      }\n      if (typeof result.metadata !== 'undefined') {\n        info.metadata = result.metadata\n      }\n\n      if (typeof result.hasEmptyPrelude !== 'undefined') {\n        info.hasEmptyPrelude = result.hasEmptyPrelude\n      }\n\n      if (typeof result.hasPostponed !== 'undefined') {\n        info.hasPostponed = result.hasPostponed\n      }\n\n      if (typeof result.fetchMetrics !== 'undefined') {\n        info.fetchMetrics = result.fetchMetrics\n      }\n\n      collector.byPath.set(path, info)\n\n      // Update not found.\n      if (result.ssgNotFound === true) {\n        collector.ssgNotFoundPaths.add(path)\n      }\n\n      // Update durations.\n      const durations = collector.byPage.get(page) ?? {\n        durationsByPath: new Map<string, number>(),\n      }\n      durations.durationsByPath.set(path, result.duration)\n      collector.byPage.set(page, durations)\n    }\n  }\n\n  // Export mode provide static outputs that are not compatible with PPR mode.\n  if (!options.buildExport && nextConfig.experimental.ppr) {\n    // TODO: add message\n    throw new Error('Invariant: PPR cannot be enabled in export mode')\n  }\n\n  // copy prerendered routes to outDir\n  if (!options.buildExport && prerenderManifest) {\n    await Promise.all(\n      Object.keys(prerenderManifest.routes).map(async (unnormalizedRoute) => {\n        const { srcRoute } = prerenderManifest!.routes[unnormalizedRoute]\n        const appPageName = mapAppRouteToPage.get(srcRoute || '')\n        const pageName = appPageName || srcRoute || unnormalizedRoute\n        const isAppPath = Boolean(appPageName)\n        const isAppRouteHandler = appPageName && isAppRouteRoute(appPageName)\n\n        // returning notFound: true from getStaticProps will not\n        // output html/json files during the build\n        if (prerenderManifest!.notFoundRoutes.includes(unnormalizedRoute)) {\n          return\n        }\n        // TODO: This rewrites /index/foo to /index/index/foo. Investigate and\n        // fix. I presume this was because normalizePagePath was designed for\n        // some other use case and then reused here for static exports without\n        // realizing the implications.\n        const route = normalizePagePath(unnormalizedRoute)\n\n        const pagePath = getPagePath(pageName, distDir, undefined, isAppPath)\n        const distPagesDir = join(\n          pagePath,\n          // strip leading / and then recurse number of nested dirs\n          // to place from base folder\n          pageName\n            .slice(1)\n            .split('/')\n            .map(() => '..')\n            .join('/')\n        )\n\n        const orig = join(distPagesDir, route)\n        const handlerSrc = `${orig}.body`\n        const handlerDest = join(outDir, route)\n\n        if (isAppRouteHandler && existsSync(handlerSrc)) {\n          await fs.mkdir(dirname(handlerDest), { recursive: true })\n          await fs.copyFile(handlerSrc, handlerDest)\n          return\n        }\n\n        const htmlDest = join(\n          outDir,\n          `${route}${\n            subFolders && route !== '/index' ? `${sep}index` : ''\n          }.html`\n        )\n        const ampHtmlDest = join(\n          outDir,\n          `${route}.amp${subFolders ? `${sep}index` : ''}.html`\n        )\n        const jsonDest = isAppPath\n          ? join(\n              outDir,\n              `${route}${\n                subFolders && route !== '/index' ? `${sep}index` : ''\n              }.txt`\n            )\n          : join(pagesDataDir, `${route}.json`)\n\n        await fs.mkdir(dirname(htmlDest), { recursive: true })\n        await fs.mkdir(dirname(jsonDest), { recursive: true })\n\n        const htmlSrc = `${orig}.html`\n        const jsonSrc = `${orig}${isAppPath ? RSC_SUFFIX : '.json'}`\n\n        await fs.copyFile(htmlSrc, htmlDest)\n        await fs.copyFile(jsonSrc, jsonDest)\n\n        if (existsSync(`${orig}.amp.html`)) {\n          await fs.mkdir(dirname(ampHtmlDest), { recursive: true })\n          await fs.copyFile(`${orig}.amp.html`, ampHtmlDest)\n        }\n\n        const segmentsDir = `${orig}${RSC_SEGMENTS_DIR_SUFFIX}`\n        if (isAppPath && existsSync(segmentsDir)) {\n          // Output a data file for each of this page's segments\n          //\n          // These files are requested by the client router's internal\n          // prefetcher, not the user directly. So we don't need to account for\n          // things like trailing slash handling.\n          //\n          // To keep the protocol simple, we can use the non-normalized route\n          // path instead of the normalized one (which, among other things,\n          // rewrites `/` to `/index`).\n          const segmentsDirDest = join(outDir, unnormalizedRoute)\n          const segmentPaths = await collectSegmentPaths(segmentsDir)\n          await Promise.all(\n            segmentPaths.map(async (segmentFileSrc) => {\n              const segmentPath =\n                '/' + segmentFileSrc.slice(0, -RSC_SEGMENT_SUFFIX.length)\n              const segmentFilename =\n                convertSegmentPathToStaticExportFilename(segmentPath)\n              const segmentFileDest = join(segmentsDirDest, segmentFilename)\n              await fs.mkdir(dirname(segmentFileDest), { recursive: true })\n              await fs.copyFile(\n                join(segmentsDir, segmentFileSrc),\n                segmentFileDest\n              )\n            })\n          )\n        }\n      })\n    )\n  }\n\n  if (Object.keys(ampValidations).length) {\n    console.log(formatAmpMessages(ampValidations))\n  }\n  if (hadValidationError) {\n    throw new ExportError(\n      `AMP Validation caused the export to fail. https://nextjs.org/docs/messages/amp-export-validation`\n    )\n  }\n\n  if (failedExportAttemptsByPage.size > 0) {\n    const failedPages = Array.from(failedExportAttemptsByPage.keys())\n    throw new ExportError(\n      `Export encountered errors on following paths:\\n\\t${failedPages\n        .sort()\n        .join('\\n\\t')}`\n    )\n  }\n\n  await fs.writeFile(\n    join(distDir, EXPORT_DETAIL),\n    formatManifest({\n      version: 1,\n      outDirectory: outDir,\n      success: true,\n    }),\n    'utf8'\n  )\n\n  if (telemetry) {\n    await telemetry.flush()\n  }\n\n  await worker.end()\n\n  return collector\n}\n\nasync function collectSegmentPaths(segmentsDirectory: string) {\n  const results: Array<string> = []\n  await collectSegmentPathsImpl(segmentsDirectory, segmentsDirectory, results)\n  return results\n}\n\nasync function collectSegmentPathsImpl(\n  segmentsDirectory: string,\n  directory: string,\n  results: Array<string>\n) {\n  const segmentFiles = await fs.readdir(directory, {\n    withFileTypes: true,\n  })\n  await Promise.all(\n    segmentFiles.map(async (segmentFile) => {\n      if (segmentFile.isDirectory()) {\n        await collectSegmentPathsImpl(\n          segmentsDirectory,\n          join(directory, segmentFile.name),\n          results\n        )\n        return\n      }\n      if (!segmentFile.name.endsWith(RSC_SEGMENT_SUFFIX)) {\n        return\n      }\n      results.push(\n        relative(segmentsDirectory, join(directory, segmentFile.name))\n      )\n    })\n  )\n}\n\nexport default async function exportApp(\n  dir: string,\n  options: ExportAppOptions,\n  span: Span\n): Promise<ExportAppResult | null> {\n  const nextExportSpan = span.traceChild('next-export')\n\n  return nextExportSpan.traceAsyncFn(async () => {\n    return await exportAppImpl(dir, options, nextExportSpan)\n  })\n}\n"], "names": ["ExportError", "exportApp", "Error", "code", "exportAppImpl", "dir", "options", "span", "nextConfig", "resolve", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "loadEnvConfig", "Log", "enabledDirectories", "traceAsyncFn", "loadConfig", "PHASE_EXPORT", "distDir", "join", "telemetry", "buildExport", "Telemetry", "record", "eventCliSession", "webpackVersion", "cliCommand", "isSrcDir", "has<PERSON>ow<PERSON><PERSON>", "findUp", "cwd", "isCustomServer", "turboFlag", "pagesDir", "appDir", "subFolders", "trailingSlash", "silent", "info", "buildIdFile", "BUILD_ID_FILE", "existsSync", "customRoutes", "filter", "config", "hasNextSupport", "length", "warn", "buildId", "fs", "readFile", "pagesManifest", "pages", "require", "SERVER_DIRECTORY", "PAGES_MANIFEST", "prerenderManifest", "PRERENDER_MANIFEST", "appRoutePathManifest", "APP_PATH_ROUTES_MANIFEST", "err", "isError", "undefined", "excludedPrerenderRoutes", "Set", "Object", "keys", "defaultPathMap", "hasApiRoutes", "page", "isAPIRoute", "dynamicRoutes", "add", "mapAppRouteToPage", "Map", "pageName", "routePath", "entries", "set", "isAppPageRoute", "routes", "_isAppDir", "outDir", "outdir", "rm", "recursive", "force", "mkdir", "writeFile", "EXPORT_DETAIL", "formatManifest", "version", "outDirectory", "success", "recursiveCopy", "CLIENT_STATIC_FILES_PATH", "exportPathMap", "defaultMap", "i18n", "images", "loader", "unoptimized", "isNextImageImported", "EXPORT_MARKER", "then", "text", "JSON", "parse", "catch", "serverActionsManifest", "app", "SERVER_REFERENCE_MANIFEST", "output", "routesManifest", "ROUTES_MANIFEST", "rewrites", "beforeFiles", "hasInterceptionRouteRewrite", "some", "isInterceptionRouteRewrite", "actionIds", "node", "edge", "actionId", "extractInfoFromServerReferenceId", "type", "renderOpts", "previewProps", "preview", "nextExport", "assetPrefix", "replace", "dev", "basePath", "canonicalBase", "amp", "ampSkipValidation", "experimental", "skipValidation", "ampOptimizerConfig", "optimizer", "locales", "locale", "defaultLocale", "domainLocales", "domains", "disableOptimizedLoading", "supportsDynamicResponse", "crossOrigin", "optimizeCss", "nextConfigOutput", "nextScriptWorkers", "largePageDataBytes", "serverActions", "serverComponents", "cacheLifeProfiles", "cacheLife", "nextFontManifest", "NEXT_FONT_MANIFEST", "strictNextHead", "deploymentId", "htmlLimitedBots", "source", "clientTraceMetadata", "expireTime", "dynamicIO", "clientSegmentCache", "Boolean", "dynamicOnHover", "inlineCss", "authInterrupts", "reactMaxHeadersLength", "publicRuntimeConfig", "runtimeConfig", "globalThis", "__NEXT_DATA__", "exportMap", "exportPaths", "map", "path", "denormalizePagePath", "normalizePagePath", "filteredPaths", "route", "fallbackEnabledPages", "prerenderInfo", "fallback", "size", "SSG_FALLBACK_EXPORT_ERROR", "hasMiddleware", "functionsConfigManifest", "middlewareManifest", "MIDDLEWARE_MANIFEST", "FUNCTIONS_CONFIG_MANIFEST", "middleware", "functions", "yellow", "bold", "pagesDataDir", "ampValidations", "publicDir", "CLIENT_PUBLIC_FILES_PATH", "failedExportAttemptsByPage", "minChunkSize", "staticGenerationMinPagesPerWorker", "numWorkers", "Math", "min", "ceil", "chunkSize", "chunks", "Array", "from", "_", "i", "slice", "remainingPages", "for<PERSON>ach", "index", "push", "progress", "createProgress", "statusMessage", "worker", "createStaticWorker", "results", "Promise", "all", "paths", "exportPages", "parentSpanId", "getId", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCache", "fetchCacheKeyPrefix", "flat", "hadValidationError", "collector", "by<PERSON><PERSON>", "byPage", "ssgNotFoundPaths", "turborepoAccessTraceResults", "result", "page<PERSON><PERSON>", "turborepoAccessTraceResult", "TurborepoAccessTraceResult", "fromSerialized", "validation", "errors", "get", "cacheControl", "metadata", "hasEmptyPrelude", "hasPostponed", "fetchMetrics", "ssgNotFound", "durations", "durationsByPath", "duration", "ppr", "unnormalizedRoute", "srcRoute", "appPageName", "isAppPath", "isAppRouteHandler", "isAppRouteRoute", "notFoundRoutes", "includes", "pagePath", "getPagePath", "distPagesDir", "split", "orig", "handlerSrc", "handlerDest", "dirname", "copyFile", "htmlDest", "sep", "ampHtmlDest", "jsonDest", "htmlSrc", "jsonSrc", "RSC_SUFFIX", "segmentsDir", "RSC_SEGMENTS_DIR_SUFFIX", "segmentsDirDest", "segmentPaths", "collectSegmentPaths", "segmentFileSrc", "segmentPath", "RSC_SEGMENT_SUFFIX", "segmentFilename", "convertSegmentPathToStaticExportFilename", "segmentFileDest", "console", "log", "formatAmpMessages", "failedPages", "sort", "flush", "end", "segmentsDirectory", "collectSegmentPathsImpl", "directory", "segmentFiles", "readdir", "withFileTypes", "segmentFile", "isDirectory", "name", "endsWith", "relative", "nextExportSpan"], "mappings": ";;;;;;;;;;;;;;;IAkEaA,WAAW;eAAXA;;IAiyBb,OAUC;eAV6BC;;;uBA91B6B;4BAG9B;+DACV;oBACwB;QAEpC;sBAE+C;uBACpB;6DAEb;2BAMd;+BACuB;4BAiBvB;+DACgB;wBAES;wBACD;yBACL;mCACQ;qCACE;qBACN;4BACH;yBACC;iCAGI;gCACD;gEACX;gCACW;sCACY;0BACZ;oDAEY;qCAEM;sCACQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElD,MAAMD,oBAAoBE;;QAA1B,qBACLC,OAAO;;AACT;AAEA,eAAeC,cACbC,GAAW,EACXC,OAAmC,EACnCC,IAAU;QA8ROC,iBACIA,8BACCA;IA9RtBH,MAAMI,IAAAA,aAAO,EAACJ;IAEd,4EAA4E;IAC5EE,KAAKG,UAAU,CAAC,eAAeC,OAAO,CAAC,IAAMC,IAAAA,kBAAa,EAACP,KAAK,OAAOQ;IAEvE,MAAM,EAAEC,kBAAkB,EAAE,GAAGR;IAE/B,MAAME,aACJF,QAAQE,UAAU,IACjB,MAAMD,KACJG,UAAU,CAAC,oBACXK,YAAY,CAAC,IAAMC,IAAAA,eAAU,EAACC,wBAAY,EAAEZ;IAEjD,MAAMa,UAAUC,IAAAA,UAAI,EAACd,KAAKG,WAAWU,OAAO;IAC5C,MAAME,YAAYd,QAAQe,WAAW,GAAG,OAAO,IAAIC,kBAAS,CAAC;QAAEJ;IAAQ;IAEvE,IAAIE,WAAW;QACbA,UAAUG,MAAM,CACdC,IAAAA,uBAAe,EAACN,SAASV,YAAY;YACnCiB,gBAAgB;YAChBC,YAAY;YACZC,UAAU;YACVC,YAAY,CAAC,CAAE,MAAMC,IAAAA,eAAM,EAAC,YAAY;gBAAEC,KAAKzB;YAAI;YACnD0B,gBAAgB;YAChBC,WAAW;YACXC,UAAU;YACVC,QAAQ;QACV;IAEJ;IAEA,MAAMC,aAAa3B,WAAW4B,aAAa,IAAI,CAAC9B,QAAQe,WAAW;IAEnE,IAAI,CAACf,QAAQ+B,MAAM,IAAI,CAAC/B,QAAQe,WAAW,EAAE;QAC3CR,KAAIyB,IAAI,CAAC,CAAC,uBAAuB,EAAEpB,SAAS;IAC9C;IAEA,MAAMqB,cAAcpB,IAAAA,UAAI,EAACD,SAASsB,yBAAa;IAE/C,IAAI,CAACC,IAAAA,cAAU,EAACF,cAAc;QAC5B,MAAM,qBAEL,CAFK,IAAIvC,YACR,CAAC,0CAA0C,EAAEkB,QAAQ,gJAAgJ,CAAC,GADlM,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMwB,eAAe;QAAC;QAAY;QAAa;KAAU,CAACC,MAAM,CAC9D,CAACC,SAAW,OAAOpC,UAAU,CAACoC,OAAO,KAAK;IAG5C,IAAI,CAACC,sBAAc,IAAI,CAACvC,QAAQe,WAAW,IAAIqB,aAAaI,MAAM,GAAG,GAAG;QACtEjC,KAAIkC,IAAI,CACN,CAAC,4FAA4F,EAAEL,aAAavB,IAAI,CAC9G,MACA,+EAA+E,CAAC;IAEtF;IAEA,MAAM6B,UAAU,MAAMC,YAAE,CAACC,QAAQ,CAACX,aAAa;IAE/C,MAAMY,gBACJ,CAAC7C,QAAQ8C,KAAK,IACbC,QAAQlC,IAAAA,UAAI,EAACD,SAASoC,4BAAgB,EAAEC,0BAAc;IAEzD,IAAIC;IACJ,IAAI;QACFA,oBAAoBH,QAAQlC,IAAAA,UAAI,EAACD,SAASuC,8BAAkB;IAC9D,EAAE,OAAM,CAAC;IAET,IAAIC;IACJ,IAAI;QACFA,uBAAuBL,QAAQlC,IAAAA,UAAI,EAACD,SAASyC,oCAAwB;IACvE,EAAE,OAAOC,KAAK;QACZ,IACEC,IAAAA,gBAAO,EAACD,QACPA,CAAAA,IAAIzD,IAAI,KAAK,YAAYyD,IAAIzD,IAAI,KAAK,kBAAiB,GACxD;YACA,0DAA0D;YAC1D,oCAAoC;YACpCuD,uBAAuBI;QACzB,OAAO;YACL,2CAA2C;YAC3C,MAAMF;QACR;IACF;IAEA,MAAMG,0BAA0B,IAAIC;IACpC,MAAMZ,QAAQ9C,QAAQ8C,KAAK,IAAIa,OAAOC,IAAI,CAACf;IAC3C,MAAMgB,iBAAgC,CAAC;IAEvC,IAAIC,eAAe;IACnB,KAAK,MAAMC,QAAQjB,MAAO;QACxB,wCAAwC;QACxC,0CAA0C;QAC1C,mCAAmC;QAEnC,IAAIkB,IAAAA,sBAAU,EAACD,OAAO;YACpBD,eAAe;YACf;QACF;QAEA,IAAIC,SAAS,gBAAgBA,SAAS,WAAWA,SAAS,WAAW;YACnE;QACF;QAEA,qEAAqE;QACrE,yEAAyE;QACzE,yEAAyE;QACzE,8CAA8C;QAC9C,IAAIb,qCAAAA,kBAAmBe,aAAa,CAACF,KAAK,EAAE;YAC1CN,wBAAwBS,GAAG,CAACH;YAC5B;QACF;QAEAF,cAAc,CAACE,KAAK,GAAG;YAAEA;QAAK;IAChC;IAEA,MAAMI,oBAAoB,IAAIC;IAC9B,IAAI,CAACpE,QAAQe,WAAW,IAAIqC,sBAAsB;QAChD,KAAK,MAAM,CAACiB,UAAUC,UAAU,IAAIX,OAAOY,OAAO,CAACnB,sBAAuB;YACxEe,kBAAkBK,GAAG,CAACF,WAAWD;YACjC,IACEI,IAAAA,8BAAc,EAACJ,aACf,EAACnB,qCAAAA,kBAAmBwB,MAAM,CAACJ,UAAU,KACrC,EAACpB,qCAAAA,kBAAmBe,aAAa,CAACK,UAAU,GAC5C;gBACAT,cAAc,CAACS,UAAU,GAAG;oBAC1BP,MAAMM;oBACNM,WAAW;gBACb;YACF;QACF;IACF;IAEA,kCAAkC;IAClC,MAAMC,SAAS5E,QAAQ6E,MAAM;IAE7B,IAAID,WAAW/D,IAAAA,UAAI,EAACd,KAAK,WAAW;QAClC,MAAM,qBAEL,CAFK,IAAIL,YACR,CAAC,wJAAwJ,CAAC,GADtJ,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIkF,WAAW/D,IAAAA,UAAI,EAACd,KAAK,WAAW;QAClC,MAAM,qBAEL,CAFK,IAAIL,YACR,CAAC,wJAAwJ,CAAC,GADtJ,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMiD,YAAE,CAACmC,EAAE,CAACF,QAAQ;QAAEG,WAAW;QAAMC,OAAO;IAAK;IACnD,MAAMrC,YAAE,CAACsC,KAAK,CAACpE,IAAAA,UAAI,EAAC+D,QAAQ,SAASlC,UAAU;QAAEqC,WAAW;IAAK;IAEjE,MAAMpC,YAAE,CAACuC,SAAS,CAChBrE,IAAAA,UAAI,EAACD,SAASuE,yBAAa,GAC3BC,IAAAA,8BAAc,EAAC;QACbC,SAAS;QACTC,cAAcV;QACdW,SAAS;IACX,IACA;IAGF,wBAAwB;IACxB,IAAI,CAACvF,QAAQe,WAAW,IAAIoB,IAAAA,cAAU,EAACtB,IAAAA,UAAI,EAACd,KAAK,YAAY;QAC3D,IAAI,CAACC,QAAQ+B,MAAM,EAAE;YACnBxB,KAAIyB,IAAI,CAAC;QACX;QACA,MAAM/B,KACHG,UAAU,CAAC,yBACXK,YAAY,CAAC,IACZ+E,IAAAA,4BAAa,EAAC3E,IAAAA,UAAI,EAACd,KAAK,WAAWc,IAAAA,UAAI,EAAC+D,QAAQ;IAEtD;IAEA,8BAA8B;IAC9B,IACE,CAAC5E,QAAQe,WAAW,IACpBoB,IAAAA,cAAU,EAACtB,IAAAA,UAAI,EAACD,SAAS6E,oCAAwB,IACjD;QACA,IAAI,CAACzF,QAAQ+B,MAAM,EAAE;YACnBxB,KAAIyB,IAAI,CAAC;QACX;QACA,MAAM/B,KACHG,UAAU,CAAC,8BACXK,YAAY,CAAC,IACZ+E,IAAAA,4BAAa,EACX3E,IAAAA,UAAI,EAACD,SAAS6E,oCAAwB,GACtC5E,IAAAA,UAAI,EAAC+D,QAAQ,SAASa,oCAAwB;IAGtD;IAEA,6CAA6C;IAC7C,IAAI,OAAOvF,WAAWwF,aAAa,KAAK,YAAY;QAClDxF,WAAWwF,aAAa,GAAG,OAAOC;YAChC,OAAOA;QACT;IACF;IAEA,MAAM,EACJC,IAAI,EACJC,QAAQ,EAAEC,SAAS,SAAS,EAAEC,WAAW,EAAE,EAC5C,GAAG7F;IAEJ,IAAI0F,QAAQ,CAAC5F,QAAQe,WAAW,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAIrB,YACR,CAAC,8IAA8I,CAAC,GAD5I,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,CAACM,QAAQe,WAAW,EAAE;QACxB,MAAM,EAAEiF,mBAAmB,EAAE,GAAG,MAAM/F,KACnCG,UAAU,CAAC,0BACXK,YAAY,CAAC,IACZkC,YAAE,CACCC,QAAQ,CAAC/B,IAAAA,UAAI,EAACD,SAASqF,yBAAa,GAAG,QACvCC,IAAI,CAAC,CAACC,OAASC,KAAKC,KAAK,CAACF,OAC1BG,KAAK,CAAC,IAAO,CAAA,CAAC,CAAA;QAGrB,IACEN,uBACAF,WAAW,aACX,CAACC,eACD,CAACxD,sBAAc,EACf;YACA,MAAM,qBAML,CANK,IAAI7C,YACR,CAAC;;;;8DAIqD,CAAC,GALnD,qBAAA;uBAAA;4BAAA;8BAAA;YAMN;QACF;IACF;IAEA,IAAI6G;IACJ,IAAI/F,mBAAmBgG,GAAG,EAAE;QAC1BD,wBAAwBxD,QACtBlC,IAAAA,UAAI,EAACD,SAASoC,4BAAgB,EAAEyD,qCAAyB,GAAG;QAG9D,IAAIvG,WAAWwG,MAAM,KAAK,UAAU;gBAK9BC,sCAAAA;YAJJ,MAAMA,iBAAiB5D,QAAQlC,IAAAA,UAAI,EAACD,SAASgG,2BAAe;YAE5D,2FAA2F;YAC3F,6DAA6D;YAC7D,IAAID,CAAAA,mCAAAA,2BAAAA,eAAgBE,QAAQ,sBAAxBF,uCAAAA,yBAA0BG,WAAW,qBAArCH,qCAAuCnE,MAAM,IAAG,GAAG;gBACrD,MAAMuE,8BACJJ,eAAeE,QAAQ,CAACC,WAAW,CAACE,IAAI,CAACC,8DAA0B;gBAErE,IAAIF,6BAA6B;oBAC/B,MAAM,qBAEL,CAFK,IAAIrH,YACR,CAAC,yKAAyK,CAAC,GADvK,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,MAAMwH,YAAY;mBACbvD,OAAOC,IAAI,CAAC2C,sBAAsBY,IAAI;mBACtCxD,OAAOC,IAAI,CAAC2C,sBAAsBa,IAAI;aAC1C;YAED,IACEF,UAAUF,IAAI,CACZ,CAACK,WACCC,IAAAA,qDAAgC,EAACD,UAAUE,IAAI,KAAK,kBAExD;gBACA,MAAM,qBAEL,CAFK,IAAI7H,YACR,CAAC,oKAAoK,CAAC,GADlK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,8BAA8B;IAC9B,MAAM8H,aAAsC;QAC1CC,YAAY,EAAEvE,qCAAAA,kBAAmBwE,OAAO;QACxCC,YAAY;QACZC,aAAa1H,WAAW0H,WAAW,CAACC,OAAO,CAAC,OAAO;QACnDjH;QACAkH,KAAK;QACLC,UAAU7H,WAAW6H,QAAQ;QAC7BjG,eAAe5B,WAAW4B,aAAa;QACvCkG,eAAe9H,EAAAA,kBAAAA,WAAW+H,GAAG,qBAAd/H,gBAAgB8H,aAAa,KAAI;QAChDE,mBAAmBhI,EAAAA,+BAAAA,WAAWiI,YAAY,CAACF,GAAG,qBAA3B/H,6BAA6BkI,cAAc,KAAI;QAClEC,oBAAoBnI,EAAAA,gCAAAA,WAAWiI,YAAY,CAACF,GAAG,qBAA3B/H,8BAA6BoI,SAAS,KAAI9E;QAC9D+E,OAAO,EAAE3C,wBAAAA,KAAM2C,OAAO;QACtBC,MAAM,EAAE5C,wBAAAA,KAAM6C,aAAa;QAC3BA,aAAa,EAAE7C,wBAAAA,KAAM6C,aAAa;QAClCC,aAAa,EAAE9C,wBAAAA,KAAM+C,OAAO;QAC5BC,yBAAyB1I,WAAWiI,YAAY,CAACS,uBAAuB;QACxE,wDAAwD;QACxDC,yBAAyB;QACzBC,aAAa5I,WAAW4I,WAAW;QACnCC,aAAa7I,WAAWiI,YAAY,CAACY,WAAW;QAChDC,kBAAkB9I,WAAWwG,MAAM;QACnCuC,mBAAmB/I,WAAWiI,YAAY,CAACc,iBAAiB;QAC5DC,oBAAoBhJ,WAAWiI,YAAY,CAACe,kBAAkB;QAC9DC,eAAejJ,WAAWiI,YAAY,CAACgB,aAAa;QACpDC,kBAAkB5I,mBAAmBgG,GAAG;QACxC6C,mBAAmBnJ,WAAWiI,YAAY,CAACmB,SAAS;QACpDC,kBAAkBxG,QAChBlC,IAAAA,UAAI,EAACD,SAAS,UAAU,GAAG4I,8BAAkB,CAAC,KAAK,CAAC;QAEtD3D,QAAQ3F,WAAW2F,MAAM;QACzB,GAAIrF,mBAAmBgG,GAAG,GACtB;YACED;QACF,IACA,CAAC,CAAC;QACNkD,gBAAgBvJ,WAAWiI,YAAY,CAACsB,cAAc,IAAI;QAC1DC,cAAcxJ,WAAWwJ,YAAY;QACrCC,iBAAiBzJ,WAAWyJ,eAAe,CAACC,MAAM;QAClDzB,cAAc;YACZ0B,qBAAqB3J,WAAWiI,YAAY,CAAC0B,mBAAmB;YAChEC,YAAY5J,WAAW4J,UAAU;YACjCC,WAAW7J,WAAWiI,YAAY,CAAC4B,SAAS,IAAI;YAChDC,oBACE9J,WAAWiI,YAAY,CAAC6B,kBAAkB,KAAK,gBAC3C,gBACAC,QAAQ/J,WAAWiI,YAAY,CAAC6B,kBAAkB;YACxDE,gBAAgBhK,WAAWiI,YAAY,CAAC+B,cAAc,IAAI;YAC1DC,WAAWjK,WAAWiI,YAAY,CAACgC,SAAS,IAAI;YAChDC,gBAAgB,CAAC,CAAClK,WAAWiI,YAAY,CAACiC,cAAc;QAC1D;QACAC,uBAAuBnK,WAAWmK,qBAAqB;IACzD;IAEA,MAAM,EAAEC,mBAAmB,EAAE,GAAGpK;IAEhC,IAAIyD,OAAOC,IAAI,CAAC0G,qBAAqB9H,MAAM,GAAG,GAAG;QAC/CgF,WAAW+C,aAAa,GAAGD;IAC7B;IAEA,wDAAwD;;IACtDE,WAAmBC,aAAa,GAAG;QACnC9C,YAAY;IACd;IAEA,MAAMjC,gBAAgB,MAAMzF,KACzBG,UAAU,CAAC,uBACXK,YAAY,CAAC;QACZ,MAAMiK,YAAY,MAAMxK,WAAWwF,aAAa,CAAC7B,gBAAgB;YAC/DiE,KAAK;YACL/H;YACA6E;YACAhE;YACA8B;QACF;QACA,OAAOgI;IACT;IAEF,wDAAwD;IACxD,IAAI,CAAC1K,QAAQe,WAAW,EAAE;QACxB,4DAA4D;QAC5D,IAAI,CAAC2E,aAAa,CAAC,OAAO,EAAE;YAC1BA,aAAa,CAAC,OAAO,GAAG;gBAAE3B,MAAM;YAAU;QAC5C;QAEA;;;KAGC,GACD,IAAI,CAAC2B,aAAa,CAAC,YAAY,EAAE;YAC/B,yEAAyE;YACzEA,aAAa,CAAC,YAAY,GAAGA,aAAa,CAAC,OAAO;QACpD;IACF;IAEA,kCAAkC;IAClC,MAAMiF,cAAc;WACf,IAAIjH,IACLC,OAAOC,IAAI,CAAC8B,eAAekF,GAAG,CAAC,CAACC,OAC9BC,IAAAA,wCAAmB,EAACC,IAAAA,oCAAiB,EAACF;KAG3C;IAED,MAAMG,gBAAgBL,YAAYtI,MAAM,CACtC,CAAC4I,QACCvF,aAAa,CAACuF,MAAM,CAACtG,SAAS,IAC9B,oBAAoB;QACpB,CAACX,IAAAA,sBAAU,EAAC0B,aAAa,CAACuF,MAAM,CAAClH,IAAI;IAGzC,IAAIiH,cAAcxI,MAAM,KAAKmI,YAAYnI,MAAM,EAAE;QAC/CsB,eAAe;IACjB;IAEA,IAAIkH,cAAcxI,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,IAAIU,qBAAqB,CAAClD,QAAQe,WAAW,EAAE;QAC7C,MAAMmK,uBAAuB,IAAIxH;QAEjC,KAAK,MAAMmH,QAAQlH,OAAOC,IAAI,CAAC8B,eAAgB;YAC7C,MAAM3B,OAAO2B,aAAa,CAACmF,KAAK,CAAC9G,IAAI;YACrC,MAAMoH,gBAAgBjI,kBAAkBe,aAAa,CAACF,KAAK;YAE3D,IAAIoH,iBAAiBA,cAAcC,QAAQ,KAAK,OAAO;gBACrDF,qBAAqBhH,GAAG,CAACH;YAC3B;QACF;QAEA,IAAImH,qBAAqBG,IAAI,GAAG,GAAG;YACjC,MAAM,qBAIL,CAJK,IAAI3L,YACR,CAAC,wCAAwC,EAAE;mBACtCwL;aACJ,CAACrK,IAAI,CAAC,MAAM,EAAE,EAAEyK,oCAAyB,CAAC,EAAE,CAAC,GAH1C,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IACA,IAAIC,gBAAgB;IAEpB,IAAI,CAACvL,QAAQe,WAAW,EAAE;QACxB,IAAI;gBAWQyK;YAVV,MAAMC,qBAAqB1I,QACzBlC,IAAAA,UAAI,EAACD,SAASoC,4BAAgB,EAAE0I,+BAAmB;YAGrD,MAAMF,0BAA0BzI,QAC9BlC,IAAAA,UAAI,EAACD,SAASoC,4BAAgB,EAAE2I,qCAAyB;YAG3DJ,gBACE5H,OAAOC,IAAI,CAAC6H,mBAAmBG,UAAU,EAAEpJ,MAAM,GAAG,KACpDyH,SAAQuB,qCAAAA,wBAAwBK,SAAS,qBAAjCL,kCAAmC,CAAC,eAAe;QAC/D,EAAE,OAAM,CAAC;QAET,kDAAkD;QAClD,IAAI1H,gBAAgByH,eAAe;YACjC,IAAIrL,WAAWwG,MAAM,KAAK,UAAU;gBAClCnG,KAAIkC,IAAI,CACNqJ,IAAAA,kBAAM,EACJ,CAAC,kGAAkG,CAAC,IAEpG,CAAC,EAAE,CAAC,GACJA,IAAAA,kBAAM,EACJ,CAAC,mDAAmD,CAAC,GACnD,MACAC,IAAAA,gBAAI,EAAC,CAAC,8CAA8C,CAAC,KAEzD,CAAC,EAAE,CAAC,GACJD,IAAAA,kBAAM,EACJ,CAAC,2KAA2K,CAAC,IAE/K,CAAC,EAAE,CAAC,GACJA,IAAAA,kBAAM,EACJ,CAAC,qEAAqE,CAAC;YAG/E;QACF;IACF;IAEA,MAAME,eAAehM,QAAQe,WAAW,GACpC6D,SACA/D,IAAAA,UAAI,EAAC+D,QAAQ,cAAclC;IAE/B,MAAMuJ,iBAAgC,CAAC;IAEvC,MAAMC,YAAYrL,IAAAA,UAAI,EAACd,KAAKoM,oCAAwB;IACpD,wBAAwB;IACxB,IAAI,CAACnM,QAAQe,WAAW,IAAIoB,IAAAA,cAAU,EAAC+J,YAAY;QACjD,IAAI,CAAClM,QAAQ+B,MAAM,EAAE;YACnBxB,KAAIyB,IAAI,CAAC;QACX;QACA,MAAM/B,KAAKG,UAAU,CAAC,yBAAyBK,YAAY,CAAC,IAC1D+E,IAAAA,4BAAa,EAAC0G,WAAWtH,QAAQ;gBAC/BvC,QAAOwI,IAAI;oBACT,8BAA8B;oBAC9B,OAAO,CAACnF,aAAa,CAACmF,KAAK;gBAC7B;YACF;IAEJ;IAEA,MAAMuB,6BAAmD,IAAIhI;IAE7D,sFAAsF;IACtF,+EAA+E;IAC/E,qFAAqF;IACrF,2BAA2B;IAC3B,MAAMiI,eACJnM,WAAWiI,YAAY,CAACmE,iCAAiC,IAAI;IAC/D,8FAA8F;IAC9F,MAAMC,aAAaC,KAAKC,GAAG,CACzBzM,QAAQuM,UAAU,EAClBC,KAAKE,IAAI,CAAC1B,cAAcxI,MAAM,GAAG6J;IAEnC,0DAA0D;IAC1D,MAAMM,YAAYH,KAAKE,IAAI,CAAC1B,cAAcxI,MAAM,GAAG+J;IACnD,MAAMK,SAASC,MAAMC,IAAI,CAAC;QAAEtK,QAAQ+J;IAAW,GAAG,CAACQ,GAAGC,IACpDhC,cAAciC,KAAK,CAACD,IAAIL,WAAW,AAACK,CAAAA,IAAI,CAAA,IAAKL;IAE/C,6BAA6B;IAC7B,MAAMO,iBAAiBlC,cAAciC,KAAK,CAACV,aAAaI;IACxDO,eAAeC,OAAO,CAAC,CAACpJ,MAAMqJ;QAC5BR,MAAM,CAACQ,QAAQR,OAAOpK,MAAM,CAAC,CAAC6K,IAAI,CAACtJ;IACrC;IAEA,MAAMuJ,WAAWC,IAAAA,wBAAc,EAC7BvC,cAAcxI,MAAM,EACpBxC,QAAQwN,aAAa,IAAI;IAG3B,MAAMC,SAASC,IAAAA,yBAAkB,EAACxN,YAAYoN;IAE9C,MAAMK,UAAU,AACd,CAAA,MAAMC,QAAQC,GAAG,CACfjB,OAAOhC,GAAG,CAAC,CAACkD,QACVL,OAAOM,WAAW,CAAC;YACjBrL;YACAoL;YACApI;YACAsI,cAAc/N,KAAKgO,KAAK;YACxBjC;YACAxE;YACAxH;YACAD;YACAa;YACAgE;YACA1E;YACAgO,cAAchO,WAAWgO,YAAY;YACrCC,oBAAoBjO,WAAWiO,kBAAkB;YACjDC,YAAY;YACZC,qBAAqBnO,WAAWiI,YAAY,CAACkG,mBAAmB;QAClE,IAEJ,EACAC,IAAI;IAEN,IAAIC,qBAAqB;IAEzB,MAAMC,YAA6B;QACjCC,QAAQ,IAAIrK;QACZsK,QAAQ,IAAItK;QACZuK,kBAAkB,IAAIjL;QACtBkL,6BAA6B,IAAIxK;IACnC;IAEA,KAAK,MAAM,EAAEyK,MAAM,EAAEhE,IAAI,EAAEiE,OAAO,EAAE,IAAInB,QAAS;QAC/C,IAAI,CAACkB,QAAQ;QACb,IAAI,WAAWA,QAAQ;YACrBzC,2BAA2B5H,GAAG,CAACsK,SAAS;YACxC;QACF;QAEA,MAAM,EAAE/K,IAAI,EAAE,GAAG2B,aAAa,CAACmF,KAAK;QAEpC,IAAIgE,OAAOE,0BAA0B,EAAE;gBACrCP;aAAAA,yCAAAA,UAAUI,2BAA2B,qBAArCJ,uCAAuChK,GAAG,CACxCqG,MACAmE,gDAA0B,CAACC,cAAc,CACvCJ,OAAOE,0BAA0B;QAGvC;QAEA,+BAA+B;QAC/B,IAAIF,OAAO5C,cAAc,EAAE;YACzB,KAAK,MAAMiD,cAAcL,OAAO5C,cAAc,CAAE;gBAC9CA,cAAc,CAACiD,WAAWnL,IAAI,CAAC,GAAGmL,WAAWL,MAAM;gBACnDN,uBAAuBW,WAAWL,MAAM,CAACM,MAAM,CAAC3M,MAAM,GAAG;YAC3D;QACF;QAEA,IAAIxC,QAAQe,WAAW,EAAE;YACvB,4BAA4B;YAC5B,MAAMiB,OAAOwM,UAAUC,MAAM,CAACW,GAAG,CAACvE,SAAS,CAAC;YAC5C,IAAIgE,OAAOQ,YAAY,EAAE;gBACvBrN,KAAKqN,YAAY,GAAGR,OAAOQ,YAAY;YACzC;YACA,IAAI,OAAOR,OAAOS,QAAQ,KAAK,aAAa;gBAC1CtN,KAAKsN,QAAQ,GAAGT,OAAOS,QAAQ;YACjC;YAEA,IAAI,OAAOT,OAAOU,eAAe,KAAK,aAAa;gBACjDvN,KAAKuN,eAAe,GAAGV,OAAOU,eAAe;YAC/C;YAEA,IAAI,OAAOV,OAAOW,YAAY,KAAK,aAAa;gBAC9CxN,KAAKwN,YAAY,GAAGX,OAAOW,YAAY;YACzC;YAEA,IAAI,OAAOX,OAAOY,YAAY,KAAK,aAAa;gBAC9CzN,KAAKyN,YAAY,GAAGZ,OAAOY,YAAY;YACzC;YAEAjB,UAAUC,MAAM,CAACjK,GAAG,CAACqG,MAAM7I;YAE3B,oBAAoB;YACpB,IAAI6M,OAAOa,WAAW,KAAK,MAAM;gBAC/BlB,UAAUG,gBAAgB,CAACzK,GAAG,CAAC2G;YACjC;YAEA,oBAAoB;YACpB,MAAM8E,YAAYnB,UAAUE,MAAM,CAACU,GAAG,CAACrL,SAAS;gBAC9C6L,iBAAiB,IAAIxL;YACvB;YACAuL,UAAUC,eAAe,CAACpL,GAAG,CAACqG,MAAMgE,OAAOgB,QAAQ;YACnDrB,UAAUE,MAAM,CAAClK,GAAG,CAACT,MAAM4L;QAC7B;IACF;IAEA,4EAA4E;IAC5E,IAAI,CAAC3P,QAAQe,WAAW,IAAIb,WAAWiI,YAAY,CAAC2H,GAAG,EAAE;QACvD,oBAAoB;QACpB,MAAM,qBAA4D,CAA5D,IAAIlQ,MAAM,oDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA2D;IACnE;IAEA,oCAAoC;IACpC,IAAI,CAACI,QAAQe,WAAW,IAAImC,mBAAmB;QAC7C,MAAM0K,QAAQC,GAAG,CACflK,OAAOC,IAAI,CAACV,kBAAkBwB,MAAM,EAAEkG,GAAG,CAAC,OAAOmF;YAC/C,MAAM,EAAEC,QAAQ,EAAE,GAAG9M,kBAAmBwB,MAAM,CAACqL,kBAAkB;YACjE,MAAME,cAAc9L,kBAAkBiL,GAAG,CAACY,YAAY;YACtD,MAAM3L,WAAW4L,eAAeD,YAAYD;YAC5C,MAAMG,YAAYjG,QAAQgG;YAC1B,MAAME,oBAAoBF,eAAeG,IAAAA,gCAAe,EAACH;YAEzD,wDAAwD;YACxD,0CAA0C;YAC1C,IAAI/M,kBAAmBmN,cAAc,CAACC,QAAQ,CAACP,oBAAoB;gBACjE;YACF;YACA,sEAAsE;YACtE,qEAAqE;YACrE,sEAAsE;YACtE,8BAA8B;YAC9B,MAAM9E,QAAQF,IAAAA,oCAAiB,EAACgF;YAEhC,MAAMQ,WAAWC,IAAAA,oBAAW,EAACnM,UAAUzD,SAAS4C,WAAW0M;YAC3D,MAAMO,eAAe5P,IAAAA,UAAI,EACvB0P,UACA,yDAAyD;YACzD,4BAA4B;YAC5BlM,SACG4I,KAAK,CAAC,GACNyD,KAAK,CAAC,KACN9F,GAAG,CAAC,IAAM,MACV/J,IAAI,CAAC;YAGV,MAAM8P,OAAO9P,IAAAA,UAAI,EAAC4P,cAAcxF;YAChC,MAAM2F,aAAa,GAAGD,KAAK,KAAK,CAAC;YACjC,MAAME,cAAchQ,IAAAA,UAAI,EAAC+D,QAAQqG;YAEjC,IAAIkF,qBAAqBhO,IAAAA,cAAU,EAACyO,aAAa;gBAC/C,MAAMjO,YAAE,CAACsC,KAAK,CAAC6L,IAAAA,aAAO,EAACD,cAAc;oBAAE9L,WAAW;gBAAK;gBACvD,MAAMpC,YAAE,CAACoO,QAAQ,CAACH,YAAYC;gBAC9B;YACF;YAEA,MAAMG,WAAWnQ,IAAAA,UAAI,EACnB+D,QACA,GAAGqG,QACDpJ,cAAcoJ,UAAU,WAAW,GAAGgG,SAAG,CAAC,KAAK,CAAC,GAAG,GACpD,KAAK,CAAC;YAET,MAAMC,cAAcrQ,IAAAA,UAAI,EACtB+D,QACA,GAAGqG,MAAM,IAAI,EAAEpJ,aAAa,GAAGoP,SAAG,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;YAEvD,MAAME,WAAWjB,YACbrP,IAAAA,UAAI,EACF+D,QACA,GAAGqG,QACDpJ,cAAcoJ,UAAU,WAAW,GAAGgG,SAAG,CAAC,KAAK,CAAC,GAAG,GACpD,IAAI,CAAC,IAERpQ,IAAAA,UAAI,EAACmL,cAAc,GAAGf,MAAM,KAAK,CAAC;YAEtC,MAAMtI,YAAE,CAACsC,KAAK,CAAC6L,IAAAA,aAAO,EAACE,WAAW;gBAAEjM,WAAW;YAAK;YACpD,MAAMpC,YAAE,CAACsC,KAAK,CAAC6L,IAAAA,aAAO,EAACK,WAAW;gBAAEpM,WAAW;YAAK;YAEpD,MAAMqM,UAAU,GAAGT,KAAK,KAAK,CAAC;YAC9B,MAAMU,UAAU,GAAGV,OAAOT,YAAYoB,qBAAU,GAAG,SAAS;YAE5D,MAAM3O,YAAE,CAACoO,QAAQ,CAACK,SAASJ;YAC3B,MAAMrO,YAAE,CAACoO,QAAQ,CAACM,SAASF;YAE3B,IAAIhP,IAAAA,cAAU,EAAC,GAAGwO,KAAK,SAAS,CAAC,GAAG;gBAClC,MAAMhO,YAAE,CAACsC,KAAK,CAAC6L,IAAAA,aAAO,EAACI,cAAc;oBAAEnM,WAAW;gBAAK;gBACvD,MAAMpC,YAAE,CAACoO,QAAQ,CAAC,GAAGJ,KAAK,SAAS,CAAC,EAAEO;YACxC;YAEA,MAAMK,cAAc,GAAGZ,OAAOa,kCAAuB,EAAE;YACvD,IAAItB,aAAa/N,IAAAA,cAAU,EAACoP,cAAc;gBACxC,sDAAsD;gBACtD,EAAE;gBACF,4DAA4D;gBAC5D,qEAAqE;gBACrE,uCAAuC;gBACvC,EAAE;gBACF,mEAAmE;gBACnE,iEAAiE;gBACjE,6BAA6B;gBAC7B,MAAME,kBAAkB5Q,IAAAA,UAAI,EAAC+D,QAAQmL;gBACrC,MAAM2B,eAAe,MAAMC,oBAAoBJ;gBAC/C,MAAM3D,QAAQC,GAAG,CACf6D,aAAa9G,GAAG,CAAC,OAAOgH;oBACtB,MAAMC,cACJ,MAAMD,eAAe3E,KAAK,CAAC,GAAG,CAAC6E,6BAAkB,CAACtP,MAAM;oBAC1D,MAAMuP,kBACJC,IAAAA,8DAAwC,EAACH;oBAC3C,MAAMI,kBAAkBpR,IAAAA,UAAI,EAAC4Q,iBAAiBM;oBAC9C,MAAMpP,YAAE,CAACsC,KAAK,CAAC6L,IAAAA,aAAO,EAACmB,kBAAkB;wBAAElN,WAAW;oBAAK;oBAC3D,MAAMpC,YAAE,CAACoO,QAAQ,CACflQ,IAAAA,UAAI,EAAC0Q,aAAaK,iBAClBK;gBAEJ;YAEJ;QACF;IAEJ;IAEA,IAAItO,OAAOC,IAAI,CAACqI,gBAAgBzJ,MAAM,EAAE;QACtC0P,QAAQC,GAAG,CAACC,IAAAA,wBAAiB,EAACnG;IAChC;IACA,IAAIsC,oBAAoB;QACtB,MAAM,qBAEL,CAFK,IAAI7O,YACR,CAAC,gGAAgG,CAAC,GAD9F,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI0M,2BAA2Bf,IAAI,GAAG,GAAG;QACvC,MAAMgH,cAAcxF,MAAMC,IAAI,CAACV,2BAA2BxI,IAAI;QAC9D,MAAM,qBAIL,CAJK,IAAIlE,YACR,CAAC,iDAAiD,EAAE2S,YACjDC,IAAI,GACJzR,IAAI,CAAC,SAAS,GAHb,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;IAEA,MAAM8B,YAAE,CAACuC,SAAS,CAChBrE,IAAAA,UAAI,EAACD,SAASuE,yBAAa,GAC3BC,IAAAA,8BAAc,EAAC;QACbC,SAAS;QACTC,cAAcV;QACdW,SAAS;IACX,IACA;IAGF,IAAIzE,WAAW;QACb,MAAMA,UAAUyR,KAAK;IACvB;IAEA,MAAM9E,OAAO+E,GAAG;IAEhB,OAAOhE;AACT;AAEA,eAAemD,oBAAoBc,iBAAyB;IAC1D,MAAM9E,UAAyB,EAAE;IACjC,MAAM+E,wBAAwBD,mBAAmBA,mBAAmB9E;IACpE,OAAOA;AACT;AAEA,eAAe+E,wBACbD,iBAAyB,EACzBE,SAAiB,EACjBhF,OAAsB;IAEtB,MAAMiF,eAAe,MAAMjQ,YAAE,CAACkQ,OAAO,CAACF,WAAW;QAC/CG,eAAe;IACjB;IACA,MAAMlF,QAAQC,GAAG,CACf+E,aAAahI,GAAG,CAAC,OAAOmI;QACtB,IAAIA,YAAYC,WAAW,IAAI;YAC7B,MAAMN,wBACJD,mBACA5R,IAAAA,UAAI,EAAC8R,WAAWI,YAAYE,IAAI,GAChCtF;YAEF;QACF;QACA,IAAI,CAACoF,YAAYE,IAAI,CAACC,QAAQ,CAACpB,6BAAkB,GAAG;YAClD;QACF;QACAnE,QAAQN,IAAI,CACV8F,IAAAA,cAAQ,EAACV,mBAAmB5R,IAAAA,UAAI,EAAC8R,WAAWI,YAAYE,IAAI;IAEhE;AAEJ;AAEe,eAAetT,UAC5BI,GAAW,EACXC,OAAyB,EACzBC,IAAU;IAEV,MAAMmT,iBAAiBnT,KAAKG,UAAU,CAAC;IAEvC,OAAOgT,eAAe3S,YAAY,CAAC;QACjC,OAAO,MAAMX,cAAcC,KAAKC,SAASoT;IAC3C;AACF"}