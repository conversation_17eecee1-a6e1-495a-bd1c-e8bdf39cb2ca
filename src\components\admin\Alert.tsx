import React from 'react';

interface AlertProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  onClose?: () => void;
  className?: string;
}

const Alert: React.FC<AlertProps> = ({
  type,
  title,
  message,
  onClose,
  className = ''
}) => {
  const getAlertStyles = () => {
    switch (type) {
      case 'success':
        return {
          container: 'bg-green-50 border-green-200 text-green-700',
          icon: 'ri-check-line text-green-600',
          closeButton: 'text-green-600 hover:text-green-800'
        };
      case 'error':
        return {
          container: 'bg-red-50 border-red-200 text-red-700',
          icon: 'ri-error-warning-line text-red-600',
          closeButton: 'text-red-600 hover:text-red-800'
        };
      case 'warning':
        return {
          container: 'bg-yellow-50 border-yellow-200 text-yellow-700',
          icon: 'ri-alert-line text-yellow-600',
          closeButton: 'text-yellow-600 hover:text-yellow-800'
        };
      case 'info':
        return {
          container: 'bg-blue-50 border-blue-200 text-blue-700',
          icon: 'ri-information-line text-blue-600',
          closeButton: 'text-blue-600 hover:text-blue-800'
        };
      default:
        return {
          container: 'bg-gray-50 border-gray-200 text-gray-700',
          icon: 'ri-information-line text-gray-600',
          closeButton: 'text-gray-600 hover:text-gray-800'
        };
    }
  };

  const styles = getAlertStyles();

  return (
    <div className={`border rounded-lg p-4 ${styles.container} ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <i className={`${styles.icon} text-lg`}></i>
        </div>
        <div className="mr-3 flex-1">
          {title && (
            <h4 className="font-medium mb-1">{title}</h4>
          )}
          <p className="text-sm">{message}</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className={`flex-shrink-0 p-1 rounded-md transition-colors duration-200 ${styles.closeButton}`}
          >
            <i className="ri-close-line text-lg"></i>
          </button>
        )}
      </div>
    </div>
  );
};

export default Alert;
