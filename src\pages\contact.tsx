import React, { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import WhatsAppButton from '../components/WhatsAppButton';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { GetStaticProps } from 'next';

const Contact = () => {
  const router = useRouter();
  const locale = (router.locale || 'ar') as 'ar' | 'en';
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // محاكاة إرسال النموذج
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
      });
    } catch {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus('idle'), 5000);
    }
  };

  return (
    <>
      <Head>
        <title>اتصل بنا - DROOB HAJER</title>
        <meta name="description" content="تواصل مع فريق دروب هاجر للحصول على استشارة مجانية حول تجهيزات الفنادق والمطاعم" />
        <meta name="keywords" content="اتصل بنا, دروب هاجر, تواصل, استشارة, تجهيزات فنادق" />
      </Head>
      
      <div className="bg-gray-50 min-h-screen">
        <Navbar locale={locale} />
        
        <main>
          {/* Hero Section */}
          <section className="relative bg-gradient-to-r from-primary to-secondary text-white py-20">
            <div className="absolute inset-0 bg-black/20"></div>
            <div className="container mx-auto px-4 relative z-10">
              <div className="text-center max-w-4xl mx-auto">
                <h1 className="text-4xl md:text-6xl font-bold mb-6">اتصل بنا</h1>
                <p className="text-xl md:text-2xl text-gray-200 leading-relaxed">
                  نحن هنا لمساعدتك في تحقيق رؤيتك وتجهيز مشروعك بأفضل الحلول
                </p>
              </div>
            </div>
          </section>

          {/* Contact Form & Info Section */}
          <section className="py-20 bg-white">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                {/* Contact Form */}
                <div>
                  <h2 className="text-3xl font-bold text-primary mb-8">أرسل لنا رسالة</h2>
                  
                  {submitStatus === 'success' && (
                    <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                      تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.
                    </div>
                  )}
                  
                  {submitStatus === 'error' && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                      حدث خطأ في إرسال الرسالة. يرجى المحاولة مرة أخرى.
                    </div>
                  )}
                  
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="name" className="block text-gray-700 font-semibold mb-2">
                          الاسم الكامل *
                        </label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                          placeholder="أدخل اسمك الكامل"
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="email" className="block text-gray-700 font-semibold mb-2">
                          البريد الإلكتروني *
                        </label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="phone" className="block text-gray-700 font-semibold mb-2">
                          رقم الهاتف *
                        </label>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                          placeholder="+966 50 000 0000"
                        />
                      </div>
                      
                      <div>
                        <label htmlFor="subject" className="block text-gray-700 font-semibold mb-2">
                          موضوع الرسالة *
                        </label>
                        <select
                          id="subject"
                          name="subject"
                          value={formData.subject}
                          onChange={handleInputChange}
                          required
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                        >
                          <option value="">اختر الموضوع</option>
                          <option value="general">استفسار عام</option>
                          <option value="quote">طلب عرض سعر</option>
                          <option value="support">دعم فني</option>
                          <option value="partnership">شراكة</option>
                          <option value="complaint">شكوى</option>
                        </select>
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="message" className="block text-gray-700 font-semibold mb-2">
                        الرسالة *
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                        rows={6}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 resize-vertical"
                        placeholder="اكتب رسالتك هنا..."
                      ></textarea>
                    </div>
                    
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-primary text-white py-4 px-8 rounded-lg font-bold hover:bg-secondary transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    >
                      {isSubmitting ? (
                        <>
                          <i className="ri-loader-4-line animate-spin"></i>
                          جاري الإرسال...
                        </>
                      ) : (
                        <>
                          <i className="ri-send-plane-line"></i>
                          إرسال الرسالة
                        </>
                      )}
                    </button>
                  </form>
                </div>

                {/* Contact Information */}
                <div>
                  <h2 className="text-3xl font-bold text-primary mb-8">معلومات التواصل</h2>
                  
                  <div className="space-y-8">
                    <div className="flex gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                        <i className="ri-map-pin-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-primary mb-2">العنوان</h3>
                        <p className="text-gray-600 leading-relaxed">
                          الرياض، المملكة العربية السعودية<br />
                          طريق الملك فهد، حي العليا<br />
                          مجمع الأعمال التجاري، الطابق الثالث
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                        <i className="ri-phone-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-primary mb-2">الهاتف</h3>
                        <p className="text-gray-600">
                          <a href="tel:+966500000000" className="hover:text-primary transition-colors">
                            +966 50 000 0000
                          </a>
                        </p>
                        <p className="text-gray-600">
                          <a href="tel:+966112345678" className="hover:text-primary transition-colors">
                            +966 11 234 5678
                          </a>
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                        <i className="ri-mail-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-primary mb-2">البريد الإلكتروني</h3>
                        <p className="text-gray-600">
                          <a href="mailto:<EMAIL>" className="hover:text-primary transition-colors">
                            <EMAIL>
                          </a>
                        </p>
                        <p className="text-gray-600">
                          <a href="mailto:<EMAIL>" className="hover:text-primary transition-colors">
                            <EMAIL>
                          </a>
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex gap-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                        <i className="ri-time-line text-xl text-primary"></i>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-primary mb-2">ساعات العمل</h3>
                        <p className="text-gray-600">
                          الأحد - الخميس: 8:00 ص - 6:00 م<br />
                          الجمعة: 2:00 م - 6:00 م<br />
                          السبت: مغلق
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Social Media */}
                  <div className="mt-12">
                    <h3 className="text-xl font-bold text-primary mb-6">تابعنا على</h3>
                    <div className="flex gap-4">
                      <a href="#" className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-all duration-300">
                        <i className="ri-facebook-fill text-xl"></i>
                      </a>
                      <a href="#" className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-all duration-300">
                        <i className="ri-twitter-x-fill text-xl"></i>
                      </a>
                      <a href="#" className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-all duration-300">
                        <i className="ri-instagram-fill text-xl"></i>
                      </a>
                      <a href="#" className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-all duration-300">
                        <i className="ri-linkedin-fill text-xl"></i>
                      </a>
                      <a href="#" className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-all duration-300">
                        <i className="ri-whatsapp-fill text-xl"></i>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Map Section */}
          <section className="py-20 bg-gray-50">
            <div className="container mx-auto px-4">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-primary mb-4">موقعنا</h2>
                <p className="text-gray-600 text-lg">
                  يمكنك زيارتنا في مقرنا الرئيسي في الرياض
                </p>
              </div>
              
              <div className="bg-white rounded-2xl p-8 shadow-lg">
                <div className="bg-gray-200 rounded-lg h-96 flex items-center justify-center">
                  <div className="text-center">
                    <i className="ri-map-pin-line text-6xl text-gray-400 mb-4"></i>
                    <p className="text-gray-600">خريطة الموقع</p>
                    <p className="text-sm text-gray-500 mt-2">
                      يمكن إضافة خريطة Google Maps هنا
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* FAQ Section */}
          <section className="py-20 bg-white">
            <div className="container mx-auto px-4">
              <div className="text-center mb-16">
                <h2 className="text-3xl font-bold text-primary mb-4">الأسئلة الشائعة</h2>
                <p className="text-gray-600 text-lg max-w-2xl mx-auto">
                  إجابات على أكثر الأسئلة شيوعاً حول خدماتنا ومنتجاتنا
                </p>
              </div>
              
              <div className="max-w-4xl mx-auto space-y-6">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-xl font-bold text-primary mb-3">ما هي مدة التوصيل؟</h3>
                  <p className="text-gray-600">
                    نحرص على توصيل المنتجات في أسرع وقت ممكن. عادة ما تتراوح مدة التوصيل من 3-7 أيام عمل حسب نوع المنتج والموقع.
                  </p>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-xl font-bold text-primary mb-3">هل تقدمون خدمة التركيب؟</h3>
                  <p className="text-gray-600">
                    نعم، نقدم خدمة التركيب والتشغيل لجميع المنتجات التي نوفرها من خلال فريق فني متخصص ومدرب.
                  </p>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-xl font-bold text-primary mb-3">ما هي شروط الضمان؟</h3>
                  <p className="text-gray-600">
                    نوفر ضمان شامل على جميع منتجاتنا يتراوح من سنة إلى 5 سنوات حسب نوع المنتج، مع خدمة صيانة دورية.
                  </p>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-xl font-bold text-primary mb-3">هل يمكنني الحصول على عرض سعر مخصص؟</h3>
                  <p className="text-gray-600">
                    بالطبع! يمكنك طلب عرض سعر مخصص لمشروعك من خلال التواصل معنا، وسيقوم فريقنا بإعداد عرض مفصل حسب احتياجاتك.
                  </p>
                </div>
              </div>
            </div>
          </section>
        </main>
        
        <Footer locale={locale} />
        <WhatsAppButton />
      </div>
    </>
  );
};

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ar', ['common'])),
    },
  };
};

export default Contact;
