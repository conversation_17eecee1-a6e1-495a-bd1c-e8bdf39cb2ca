import { NextApiRequest, NextApiResponse } from 'next';
import {
  getProducts,
  getFeaturedProducts,
  getProductsByCategory,
  getProductsBySubcategory,
  addProduct
} from '../../../utils/database';
import { AdminProduct } from '../../../types/admin';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        const { featured, categoryId, subcategoryId } = req.query;
        
        let products: AdminProduct[];

        if (featured === 'true') {
          products = getFeaturedProducts();
        } else if (categoryId && typeof categoryId === 'string') {
          products = getProductsByCategory(categoryId);
        } else if (subcategoryId && typeof subcategoryId === 'string') {
          products = getProductsBySubcategory(subcategoryId);
        } else {
          products = getProducts();
        }
        
        res.status(200).json(products);
        break;

      case 'POST':
        const newProductData = req.body as Omit<AdminProduct, 'id' | 'createdAt' | 'updatedAt'>;
        const newProduct = addProduct(newProductData);
        res.status(201).json(newProduct);
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
