import { NextApiRequest, NextApiResponse } from 'next';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // إنشاء مجلد uploads إذا لم يكن موجوداً
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    const form = formidable({
      uploadDir: uploadsDir,
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      filter: (part) => {
        // السماح فقط بالصور
        return !!(part.mimetype && part.mimetype.includes('image'));
      },
    });

    const [fields, files] = await form.parse(req);
    
    const uploadedFiles: string[] = [];
    
    // معالجة الملفات المرفوعة
    Object.values(files).forEach((fileArray) => {
      if (Array.isArray(fileArray)) {
        fileArray.forEach((file) => {
          if (file && file.filepath) {
            // إنشاء اسم ملف فريد
            const timestamp = Date.now();
            const randomString = Math.random().toString(36).substring(2, 15);
            const extension = path.extname(file.originalFilename || '');
            const newFileName = `${timestamp}_${randomString}${extension}`;
            const newFilePath = path.join(uploadsDir, newFileName);

            // نقل الملف إلى المكان الجديد
            fs.renameSync(file.filepath, newFilePath);

            // إضافة رابط الملف إلى القائمة
            uploadedFiles.push(`/uploads/${newFileName}`);
          }
        });
      } else if (fileArray && (fileArray as any).filepath) {
        // معالجة ملف واحد
        const file = fileArray as any;
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        const extension = path.extname(file.originalFilename || '');
        const newFileName = `${timestamp}_${randomString}${extension}`;
        const newFilePath = path.join(uploadsDir, newFileName);

        fs.renameSync(file.filepath, newFilePath);
        uploadedFiles.push(`/uploads/${newFileName}`);
      }
    });

    if (uploadedFiles.length === 0) {
      return res.status(400).json({ error: 'No valid files uploaded' });
    }

    res.status(200).json({ 
      success: true, 
      files: uploadedFiles,
      message: `تم رفع ${uploadedFiles.length} ملف بنجاح`
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Failed to upload files' });
  }
}
