'use client';

import React from 'react';
import { Locale } from '../lib/i18n';
import { useSiteSettings } from '../src/hooks/useSiteSettings';

interface PartnersSectionProps {
  locale: Locale;
}

const PartnersSection: React.FC<PartnersSectionProps> = ({ locale }) => {
  const { settings, loading } = useSiteSettings();

  // القيم الافتراضية في حالة عدم وجود إعدادات
  const defaultPartners = [
    {
      nameEn: 'Hilton Hotels',
      nameAr: 'فنادق هيلتون',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Hilton-Logo.png',
      description: 'Trusted partner in hospitality',
      descriptionAr: 'شريك موثوق في قطاع الفندقة'
    },
    {
      nameEn: 'Marriott',
      nameAr: 'ماريوت',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Marriott-Logo.png',
      description: 'Distinguished strategic cooperation',
      descriptionAr: 'تعاون استراتيجي مميز'
    },
    {
      nameEn: 'Four Seasons',
      nameAr: 'فور سيزونز',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Four-Seasons-Logo.png',
      description: 'Partnership in excellence and quality',
      descriptionAr: 'شراكة في التميز والجودة'
    },
    {
      nameEn: 'Hyatt',
      nameAr: 'حياة',
      logo: 'https://logos-world.net/wp-content/uploads/2020/06/Hyatt-Logo.png',
      description: 'Long-term relationship',
      descriptionAr: 'علاقة طويلة الأمد'
    }
  ];

  const defaultContent = {
    ar: {
      title: 'شركاؤنا',
      subtitle: 'نفخر بثقة كبرى الشركات والفنادق العالمية بنا',
      description: 'نعمل مع أفضل الشركات في قطاع الضيافة لتوفير أعلى معايير الجودة والخدمة',
      trustText: 'يثق بنا أكثر من',
      clientsCount: '500+',
      clientsText: 'عميل حول العالم'
    },
    en: {
      title: 'Our Partners',
      subtitle: 'We are proud of the trust of major international companies and hotels',
      description: 'We work with the best companies in the hospitality sector to provide the highest standards of quality and service',
      trustText: 'Trusted by more than',
      clientsCount: '500+',
      clientsText: 'clients worldwide'
    }
  };

  // استخدام الإعدادات من لوحة التحكم أو القيم الافتراضية
  const getPartnersContent = () => {
    if (!settings?.partnersSettings) {
      return {
        ...defaultContent[locale],
        partners: defaultPartners.map(partner => ({
          name: locale === 'ar' ? partner.nameAr : partner.nameEn,
          logo: partner.logo,
          description: locale === 'ar' ? partner.descriptionAr : partner.description
        }))
      };
    }

    return {
      title: locale === 'ar'
        ? (settings.partnersSettings.titleAr || defaultContent[locale].title)
        : (settings.partnersSettings.title || defaultContent[locale].title),
      subtitle: locale === 'ar'
        ? (settings.partnersSettings.descriptionAr || defaultContent[locale].subtitle)
        : (settings.partnersSettings.description || defaultContent[locale].subtitle),
      description: defaultContent[locale].description,
      trustText: defaultContent[locale].trustText,
      clientsCount: defaultContent[locale].clientsCount,
      clientsText: defaultContent[locale].clientsText,
      partners: settings.partnersSettings.items.length > 0
        ? settings.partnersSettings.items.map(partner => ({
            name: locale === 'ar' ? partner.nameAr : partner.nameEn,
            logo: partner.logo,
            description: locale === 'ar' ? partner.descriptionAr : partner.description,
            website: partner.website
          }))
        : defaultPartners.map(partner => ({
            name: locale === 'ar' ? partner.nameAr : partner.nameEn,
            logo: partner.logo,
            description: locale === 'ar' ? partner.descriptionAr : partner.description
          }))
    };
  };

  const currentContent = getPartnersContent();

  // عرض حالة التحميل
  if (loading) {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <i className="ri-loader-4-line text-4xl text-primary animate-spin mb-4"></i>
            <p className="text-gray-600">
              {locale === 'ar' ? 'جاري التحميل...' : 'Loading...'}
            </p>
          </div>
        </div>
      </section>
    );
  }

  // إخفاء القسم إذا كان معطلاً
  if (settings?.partnersSettings && !settings.partnersSettings.enabled) {
    return null;
  }

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">
            {currentContent.title}
          </h2>
          <p className="text-xl text-gray-600 mb-6 max-w-3xl mx-auto">
            {currentContent.subtitle}
          </p>
          <p className="text-gray-500 max-w-2xl mx-auto">
            {currentContent.description}
          </p>
        </div>

        {/* Stats */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-4 bg-white rounded-2xl px-8 py-6 shadow-lg">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-1">
                {currentContent.clientsCount}
              </div>
              <div className="text-sm text-gray-600">
                {currentContent.clientsText}
              </div>
            </div>
            <div className="w-px h-12 bg-gray-200"></div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-1">15+</div>
              <div className="text-sm text-gray-600">
                {locale === 'ar' ? 'سنة خبرة' : 'Years Experience'}
              </div>
            </div>
            <div className="w-px h-12 bg-gray-200"></div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-1">50+</div>
              <div className="text-sm text-gray-600">
                {locale === 'ar' ? 'دولة' : 'Countries'}
              </div>
            </div>
          </div>
        </div>

        {/* Partners Slider */}
        <div className="relative overflow-hidden">
          <div
            className={`flex gap-6 items-center animate-scroll ${
              locale === 'ar' ? 'animate-scroll-rtl' : 'animate-scroll-ltr'
            }`}
            style={{
              width: `${currentContent.partners.length * 220}px`,
            }}
          >
            {/* عرض الشركاء مرتين للحصول على حلقة مستمرة */}
            {[...currentContent.partners, ...currentContent.partners].map((partner, index) => (
              <div
                key={index}
                className="group relative bg-gradient-to-br from-white to-gray-50 border border-gray-100 rounded-lg p-6 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:scale-105 text-center flex-shrink-0 overflow-hidden"
                style={{ width: '200px', height: '160px' }}
              >
                {/* خلفية متدرجة عند التمرير */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* الشعار المربع */}
                <div className="relative z-10 mb-4 flex items-center justify-center">
                  <div className="w-16 h-16 bg-white rounded-lg shadow-md border border-gray-200 flex items-center justify-center group-hover:shadow-lg group-hover:border-primary/20 transition-all duration-300">
                    <img
                      src={partner.logo}
                      alt={partner.name}
                      className="w-12 h-12 object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300 rounded"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'https://via.placeholder.com/48x48?text=' + encodeURIComponent(partner.name.charAt(0));
                      }}
                    />
                  </div>
                </div>

                {/* اسم الشريك */}
                <div className="relative z-10">
                  <h3 className="font-bold text-gray-800 mb-2 text-sm truncate group-hover:text-primary transition-colors duration-300">
                    {partner.name}
                  </h3>

                  {/* خط زخرفي */}
                  <div className="w-8 h-0.5 bg-gradient-to-r from-primary to-primary/50 mx-auto mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                  {partner.description && (
                    <p className="text-xs text-gray-600 opacity-0 group-hover:opacity-100 transition-all duration-300 line-clamp-2 transform translate-y-2 group-hover:translate-y-0">
                      {partner.description}
                    </p>
                  )}

                  {partner.website && (
                    <div className="mt-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                      <a
                        href={partner.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-1 text-xs text-primary hover:text-primary/80 transition-colors duration-300 font-medium"
                      >
                        <i className="ri-external-link-line text-xs"></i>
                        {locale === 'ar' ? 'زيارة الموقع' : 'Visit Website'}
                      </a>
                    </div>
                  )}
                </div>

                {/* تأثير الإضاءة */}
                <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
              </div>
            ))}
          </div>
        </div>

        {/* CSS للحركة والتأثيرات */}
        <style jsx>{`
          @keyframes scroll-ltr {
            0% {
              transform: translateX(0);
            }
            100% {
              transform: translateX(-50%);
            }
          }

          @keyframes scroll-rtl {
            0% {
              transform: translateX(-50%);
            }
            100% {
              transform: translateX(0);
            }
          }

          .animate-scroll-ltr {
            animation: scroll-ltr 35s linear infinite;
          }

          .animate-scroll-rtl {
            animation: scroll-rtl 35s linear infinite;
          }

          .animate-scroll:hover {
            animation-play-state: paused;
          }

          .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          /* تأثيرات إضافية للبطاقات */
          .group:hover {
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
          }

          /* تأثير النبضة للشعار */
          .group:hover .w-16 {
            animation: pulse-logo 0.6s ease-in-out;
          }

          @keyframes pulse-logo {
            0%, 100% {
              transform: scale(1);
            }
            50% {
              transform: scale(1.05);
            }
          }
        `}</style>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-primary rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              {locale === 'ar' 
                ? 'هل تريد أن تكون شريكنا القادم؟' 
                : 'Want to be our next partner?'
              }
            </h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto">
              {locale === 'ar' 
                ? 'انضم إلى شبكة شركائنا المتميزين واستفد من خبرتنا الواسعة في قطاع الضيافة'
                : 'Join our distinguished partner network and benefit from our extensive experience in the hospitality sector'
              }
            </p>
            <a
              href={`/${locale}/contact`}
              className="inline-flex items-center gap-2 bg-white text-primary px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              <i className="ri-handshake-line"></i>
              {locale === 'ar' ? 'تواصل معنا' : 'Contact Us'}
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PartnersSection;
